INFO 2025-07-29 12:04:40,752 services 9585 6156480512 Starting sync of TV show requests from Overseerr
ERROR 2025-07-29 12:04:40,849 services 9585 6156480512 Overseerr API request failed: 400 Client Error: Bad Request for url: http://*************:5055/api/v1/request?take=100&skip=0&filter=tv
ERROR 2025-07-29 12:04:40,849 services 9585 6156480512 Failed to sync TV requests: API request failed: 400 Client Error: Bad Request for url: http://*************:5055/api/v1/request?take=100&skip=0&filter=tv
ERROR 2025-07-29 12:04:40,850 views 9585 6156480512 Manual data sync failed: API request failed: 400 Client Error: Bad Request for url: http://*************:5055/api/v1/request?take=100&skip=0&filter=tv
INFO 2025-07-29 12:12:22,508 services 15478 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:12:42,468 services 15704 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:12:42,546 services 15704 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:12:42,614 services 15704 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:12:42,615 services 15704 8539021056 Created new media request: 1001
INFO 2025-07-29 12:12:42,620 services 15704 8539021056 Created new TV show: Test Show
INFO 2025-07-29 12:12:42,621 services 15704 8539021056 Successfully synced 1 TV requests
INFO 2025-07-29 12:13:12,726 services 16062 8539021056 Using requests library for API calls (fallback)
ERROR 2025-07-29 12:13:12,727 services 16062 8539021056 Overseerr API request failed: Connection error
INFO 2025-07-29 12:13:12,727 services 16062 8539021056 Using requests library for API calls (fallback)
INFO 2025-07-29 12:13:12,728 services 16062 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:13:12,729 services 16062 8539021056 Created new media request: 1001
INFO 2025-07-29 12:13:12,731 services 16062 8539021056 Created new TV show: Test Show
INFO 2025-07-29 12:13:12,732 services 16062 8539021056 Successfully synced 1 TV requests
INFO 2025-07-29 12:13:21,083 services 16106 6196359168 Using python-overseerr library for API calls
INFO 2025-07-29 12:13:21,083 services 16106 6196359168 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:13:21,152 services 16106 6196359168 Created new media request: 166
ERROR 2025-07-29 12:13:21,285 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:13:21,286 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
ERROR 2025-07-29 12:13:21,287 services 16106 6196359168 Failed to sync TV show 63404: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:21,297 services 16106 6196359168 Created new media request: 165
ERROR 2025-07-29 12:13:21,436 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
WARNING 2025-07-29 12:13:21,436 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
ERROR 2025-07-29 12:13:21,436 services 16106 6196359168 Failed to sync TV show 1421: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:21,437 services 16106 6196359168 Created new media request: 164
ERROR 2025-07-29 12:13:21,520 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
WARNING 2025-07-29 12:13:21,520 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
ERROR 2025-07-29 12:13:21,520 services 16106 6196359168 Failed to sync TV show 129552: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:21,521 services 16106 6196359168 Created new media request: 163
ERROR 2025-07-29 12:13:21,589 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
WARNING 2025-07-29 12:13:21,589 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
ERROR 2025-07-29 12:13:21,589 services 16106 6196359168 Failed to sync TV show 247704: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:21,591 services 16106 6196359168 Created new media request: 162
ERROR 2025-07-29 12:13:21,684 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
WARNING 2025-07-29 12:13:21,685 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
ERROR 2025-07-29 12:13:21,685 services 16106 6196359168 Failed to sync TV show 117488: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:21,686 services 16106 6196359168 Created new media request: 161
INFO 2025-07-29 12:13:21,773 services 16106 6196359168 Created new TV show: Based on a True Story
INFO 2025-07-29 12:13:21,775 services 16106 6196359168 Created season 1 for Based on a True Story
INFO 2025-07-29 12:13:21,776 services 16106 6196359168 Created season 2 for Based on a True Story
INFO 2025-07-29 12:13:21,779 services 16106 6196359168 Created new media request: 160
INFO 2025-07-29 12:13:21,867 services 16106 6196359168 Created new TV show: The White Lotus
INFO 2025-07-29 12:13:21,868 services 16106 6196359168 Created season 1 for The White Lotus
INFO 2025-07-29 12:13:21,869 services 16106 6196359168 Created season 2 for The White Lotus
INFO 2025-07-29 12:13:21,870 services 16106 6196359168 Created season 3 for The White Lotus
INFO 2025-07-29 12:13:21,872 services 16106 6196359168 Successfully synced 7 TV requests
INFO 2025-07-29 12:13:21,872 views 16106 6196359168 Manual data sync completed successfully
INFO 2025-07-29 12:13:41,675 services 16106 6196359168 Using python-overseerr library for API calls
INFO 2025-07-29 12:13:41,676 services 16106 6196359168 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:13:41,751 services 16106 6196359168 Updated media request: 166
ERROR 2025-07-29 12:13:41,886 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:13:41,887 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
ERROR 2025-07-29 12:13:41,887 services 16106 6196359168 Failed to sync TV show 63404: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:41,891 services 16106 6196359168 Updated media request: 165
ERROR 2025-07-29 12:13:42,036 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
WARNING 2025-07-29 12:13:42,036 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
ERROR 2025-07-29 12:13:42,036 services 16106 6196359168 Failed to sync TV show 1421: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:42,039 services 16106 6196359168 Updated media request: 164
ERROR 2025-07-29 12:13:42,127 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
WARNING 2025-07-29 12:13:42,127 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
ERROR 2025-07-29 12:13:42,127 services 16106 6196359168 Failed to sync TV show 129552: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:42,129 services 16106 6196359168 Updated media request: 163
ERROR 2025-07-29 12:13:42,198 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
WARNING 2025-07-29 12:13:42,198 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
ERROR 2025-07-29 12:13:42,198 services 16106 6196359168 Failed to sync TV show 247704: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:42,199 services 16106 6196359168 Updated media request: 162
ERROR 2025-07-29 12:13:42,301 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
WARNING 2025-07-29 12:13:42,301 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
ERROR 2025-07-29 12:13:42,301 services 16106 6196359168 Failed to sync TV show 117488: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:42,303 services 16106 6196359168 Updated media request: 161
INFO 2025-07-29 12:13:42,389 services 16106 6196359168 Updated TV show: Based on a True Story
INFO 2025-07-29 12:13:42,395 services 16106 6196359168 Updated media request: 160
INFO 2025-07-29 12:13:42,482 services 16106 6196359168 Updated TV show: The White Lotus
INFO 2025-07-29 12:13:42,487 services 16106 6196359168 Successfully synced 7 TV requests
INFO 2025-07-29 12:13:42,487 views 16106 6196359168 Manual data sync completed successfully
INFO 2025-07-29 12:13:48,781 services 16489 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:13:48,782 services 16489 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:13:48,836 services 16489 8539021056 Updated media request: 166
ERROR 2025-07-29 12:13:48,963 services 16489 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:13:48,965 services 16489 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
ERROR 2025-07-29 12:13:48,967 services 16489 8539021056 Failed to sync TV show 63404: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:48,969 services 16489 8539021056 Updated media request: 165
ERROR 2025-07-29 12:13:49,127 services 16489 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
WARNING 2025-07-29 12:13:49,128 services 16489 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
ERROR 2025-07-29 12:13:49,129 services 16489 8539021056 Failed to sync TV show 1421: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:49,130 services 16489 8539021056 Updated media request: 164
ERROR 2025-07-29 12:13:49,227 services 16489 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
WARNING 2025-07-29 12:13:49,227 services 16489 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
ERROR 2025-07-29 12:13:49,227 services 16489 8539021056 Failed to sync TV show 129552: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:49,229 services 16489 8539021056 Updated media request: 163
ERROR 2025-07-29 12:13:49,292 services 16489 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
WARNING 2025-07-29 12:13:49,292 services 16489 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
ERROR 2025-07-29 12:13:49,292 services 16489 8539021056 Failed to sync TV show 247704: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:49,294 services 16489 8539021056 Updated media request: 162
ERROR 2025-07-29 12:13:49,389 services 16489 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
WARNING 2025-07-29 12:13:49,390 services 16489 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
ERROR 2025-07-29 12:13:49,390 services 16489 8539021056 Failed to sync TV show 117488: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:13:49,392 services 16489 8539021056 Updated media request: 161
INFO 2025-07-29 12:13:49,484 services 16489 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:13:49,491 services 16489 8539021056 Updated media request: 160
INFO 2025-07-29 12:13:49,578 services 16489 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:13:49,584 services 16489 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:14:18,615 services 16846 8539021056 Using python-overseerr library for API calls
ERROR 2025-07-29 12:14:18,819 services 16846 8539021056 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:14:18,821 services 16846 8539021056 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
INFO 2025-07-29 12:14:33,785 services 16106 6196359168 Using python-overseerr library for API calls
INFO 2025-07-29 12:14:33,785 services 16106 6196359168 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:14:33,852 services 16106 6196359168 Updated media request: 166
ERROR 2025-07-29 12:14:33,993 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:14:33,994 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
ERROR 2025-07-29 12:14:33,994 services 16106 6196359168 Failed to sync TV show 63404: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:14:33,997 services 16106 6196359168 Updated media request: 165
ERROR 2025-07-29 12:14:34,141 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
WARNING 2025-07-29 12:14:34,141 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
ERROR 2025-07-29 12:14:34,141 services 16106 6196359168 Failed to sync TV show 1421: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:14:34,143 services 16106 6196359168 Updated media request: 164
ERROR 2025-07-29 12:14:34,231 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
WARNING 2025-07-29 12:14:34,231 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
ERROR 2025-07-29 12:14:34,231 services 16106 6196359168 Failed to sync TV show 129552: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:14:34,232 services 16106 6196359168 Updated media request: 163
ERROR 2025-07-29 12:14:34,300 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
WARNING 2025-07-29 12:14:34,300 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
ERROR 2025-07-29 12:14:34,300 services 16106 6196359168 Failed to sync TV show 247704: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:14:34,302 services 16106 6196359168 Updated media request: 162
ERROR 2025-07-29 12:14:34,396 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
WARNING 2025-07-29 12:14:34,397 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
ERROR 2025-07-29 12:14:34,397 services 16106 6196359168 Failed to sync TV show 117488: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:14:34,399 services 16106 6196359168 Updated media request: 161
INFO 2025-07-29 12:14:34,482 services 16106 6196359168 Updated TV show: Based on a True Story
INFO 2025-07-29 12:14:34,488 services 16106 6196359168 Updated media request: 160
INFO 2025-07-29 12:14:34,581 services 16106 6196359168 Updated TV show: The White Lotus
INFO 2025-07-29 12:14:34,586 services 16106 6196359168 Successfully synced 7 TV requests
INFO 2025-07-29 12:14:34,586 views 16106 6196359168 Manual data sync completed successfully
INFO 2025-07-29 12:15:35,647 services 16106 6196359168 Using python-overseerr library for API calls
INFO 2025-07-29 12:15:35,648 services 16106 6196359168 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:15:35,726 services 16106 6196359168 Updated media request: 166
ERROR 2025-07-29 12:15:36,033 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
WARNING 2025-07-29 12:15:36,033 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2021-01-01', 'episodeCount': 5, 'id': 174526, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/gh7vz0FfqE8JsLwYvDeL4bJ7FYd.jpg'}, {'airDate': '2015-07-27', 'episodeCount': 6, 'id': 69064, 'name': 'Series 1', 'overview': 'Greg Davies is the all-powerful Taskmaster. Assisted by Alex Horne, he sets a series of devious tasks for comedians Frank Skinner, Josh Widdicombe, Roisin Conaty, Romesh Ranganathan and Tim Key.', 'seasonNumber': 1, 'posterPath': '/2ENDhZSQdhbZYFgJMmPuwMglb1p.jpg'}, {'airDate': '2016-06-20', 'episodeCount': 5, 'id': 77787, 'name': 'Series 2', 'overview': 'Greg Davies judges another comedic task force - Doc Brown, Joe Wilkinson, Jon Richardson, Katherine Ryan and Richard Osman - while the efficient but obsequious Alex Horne steers the Taskmaster ship.', 'seasonNumber': 2, 'posterPath': '/n63DSm22tEPWitizf8RrC4AEzcS.jpg'}, {'airDate': '2016-10-04', 'episodeCount': 5, 'id': 80764, 'name': 'Series 3', 'overview': 'Not many series can return for a third run and claim they have footage of five comedians making out-of-season snowmen, spreading their clothes as far and as wide as possible and whispering questions at an 83-year-old called Hugh in the hope of winning a golden version of Greg Davies’s head. Taskmaster can.', 'seasonNumber': 3, 'posterPath': '/qqnORUPIyxrWyYviYjSaPlvIqs3.jpg'}, {'airDate': '2017-04-25', 'episodeCount': 8, 'id': 87276, 'name': 'Series 4', 'overview': 'Hugh Dennis, Joe Lycett, Lolly Adefope, Mel Giedroyc and Noel Fielding all believe they have what it takes to win the most coveted prize in comedy. Let Taskmaster Greg Davies be the judge of that...', 'seasonNumber': 4, 'posterPath': '/hdwYNLjhYUSVxkxozadZhP7olC1.jpg'}, {'airDate': '2017-09-12', 'episodeCount': 8, 'id': 87277, 'name': 'Series 5', 'overview': 'Tasks are set, seals are broken and unpredictable actions take place, as Aisling Bea, Bob Mortimer, Mark Watson, Nish Kumar and Sally Phillips compete to earn the respect of Greg Davies and Alex Horne.', 'seasonNumber': 5, 'posterPath': '/6v8pOqf6gSyW81sw4USrE7VGjSF.jpg'}, {'airDate': '2018-05-02', 'episodeCount': 10, 'id': 98987, 'name': 'Series 6', 'overview': 'Alice Levine, Asim Chaudhry, Liza Tarbuck, Russell Howard and Tim Vine attempt futile tasks that inspire both breathtaking brilliance and rank incompetence. Greg Davies and Alex Horne keep score.', 'seasonNumber': 6, 'posterPath': '/AsdRJiWJGGHGEVNaRLpFmprPgAs.jpg'}, {'airDate': '2018-09-04', 'episodeCount': 10, 'id': 105904, 'name': 'Series 7', 'overview': 'James Acaster, Jessica Knappett, Kerry Godliman, Phil Wang and Rhod Gilbert take on more mostly meaningless challenges, as Taskmaster Greg Davies and his assistant Alex Horne host a seventh series.', 'seasonNumber': 7, 'posterPath': '/gVdkD9NqhKjVhrZSV6Ms8UKa1Cd.jpg'}, {'airDate': '2019-05-07', 'episodeCount': 10, 'id': 122817, 'name': 'Series 8', 'overview': 'Iain Stirling, Joe Thomas, Lou Sanders, Paul Sinha and Sian Gibson pit their wits against perfectly pointless problems in a bid to impress Taskmaster Greg Davies and his simpering secretary Alex Horne.', 'seasonNumber': 8, 'posterPath': '/87QJ1rMQC8hQpXvDj4R0NwdnXP8.jpg'}, {'airDate': '2019-09-03', 'episodeCount': 10, 'id': 127483, 'name': 'Series 9', 'overview': 'Five famous faces are put through their paces as Taskmaster Greg Davies and Little Alex Horne set David Baddiel, Ed Gamble, Jo Brand, Katy Wix and Rose Matafeo a series of mystifying missions.', 'seasonNumber': 9, 'posterPath': '/aG3GWJ5Bq2esjOJhRrg7RdrmNmH.jpg'}, {'airDate': '2020-10-14', 'episodeCount': 10, 'id': 157642, 'name': 'Series 10', 'overview': 'Taskmaster settles into its new home on Channel 4, as Greg Davies and Alex Horne put Daisy May Cooper, Johnny Vegas, Katherine Parkinson, Mawaan Rizwan and Richard Herring to the ultimate test.', 'seasonNumber': 10, 'posterPath': '/49c5JwCeo4DN1LMsXqhD49bVLO5.jpg'}, {'airDate': '2021-03-18', 'episodeCount': 10, 'id': 172583, 'name': 'Series 11', 'overview': 'Charlotte Ritchie, Jamali Maddix, Lee Mack, Mike Wozniak and Sarah Kendall are all keen as a condiment to impress Greg Davies and Alex Horne in a series of crafty tasks.', 'seasonNumber': 11, 'posterPath': '/lUxe9ac4gI0bBYFZjiNQstgfXr2.jpg'}, {'airDate': '2021-09-23', 'episodeCount': 10, 'id': 196130, 'name': 'Series 12', 'overview': "The five brave souls desperate to display Greg's regal golden head on their mantelpiece this time are: Alan Davies, Desiree Burch, Guz Khan, Morgana Robinson and Victoria Coren Mitchell.", 'seasonNumber': 12, 'posterPath': '/gInVUAwyPMCk0nvm3m6YE7INpm5.jpg'}, {'airDate': '2022-04-13', 'episodeCount': 10, 'id': 220993, 'name': 'Series 13', 'overview': "The BAFTA-winning behemoth is back for a 13th series, as Ardal O'Hanlon, Bridget Christie, Chris Ramsey, Judi Love and Sophie Duker battle to win Taskmaster Greg Davies' golden head.", 'seasonNumber': 13, 'posterPath': '/ro98U6FraLn96NngZ8tivTuUf5G.jpg'}, {'airDate': '2022-09-28', 'episodeCount': 10, 'id': 298461, 'name': 'Series 14', 'overview': 'Greg Davies and Alex Horne wreak havoc on the lives of noble knights Dara Ó Briain, Fern Brady, John Kearns, Munya Chawawa and Sarah Millican, as they battle to become the 14th Taskmaster champion.', 'seasonNumber': 14, 'posterPath': '/vpDOAtOs2NAFLpcXoycPxKXivlQ.jpg'}, {'airDate': '2023-03-30', 'episodeCount': 10, 'id': 318376, 'name': 'Series 15', 'overview': 'Greg Davies is excited to subject Frankie Boyle, Mae Martin, Jenny Eclair, Kiell Smith-Bynoe and Ivo Graham to a barrage of humiliating tasks. Little Alex Horne is excited about all the admin.', 'seasonNumber': 15, 'posterPath': '/h9KSs57ABS5J1DC0iTZkVRkQYJ8.jpg'}, {'airDate': '2023-09-20', 'episodeCount': 10, 'id': 343397, 'name': 'Series 16', 'overview': 'Greg Davies is ready to pass merciless judgement on Julian Clary, Sue Perkins, Sam Campbell, Susan Wokoma and Lucy Beaumont, while Little Alex Horne watches on from behind his clipboard.', 'seasonNumber': 16, 'posterPath': '/uaf35yNgKB9flO6igwtBbp2gnnI.jpg'}, {'airDate': '2024-03-27', 'episodeCount': 10, 'id': 365078, 'name': 'Series 17', 'overview': 'Flanked by his flunky Alex Horne, Greg Davies is primed to remorselessly judge Joanne McNally, John Robins, Nick Mohammed, Sophie Willan and Steve Pemberton. Eggs included.', 'seasonNumber': 17, 'posterPath': '/ctqMqLi9o7F3vm6k8IKhLklCny9.jpg'}, {'airDate': '2024-09-12', 'episodeCount': 10, 'id': 408479, 'name': 'Series 18', 'overview': 'Ducks. Eggs. And a honking Horne! Settle in for the 18th series of Taskmaster, as Andy Zaltzman, Babátúndé Aléshé, Emma Sidi, Jack Dee and Rosie Jones tackle the toughest endurance test on television.', 'seasonNumber': 18, 'posterPath': '/gBkSbKuqElP0dp0V20YExOXxRHQ.jpg'}, {'airDate': '2025-05-01', 'episodeCount': 10, 'id': 435545, 'name': 'Series 19', 'overview': 'Fatiha El-Ghorri, Jason Mantzoukas, Mathew Baynton, Rosie Ramsey and Stevie Martin risk their reputations as Taskmaster Greg Davies beckons the quintet of quivering comics into his world of misrule...', 'seasonNumber': 19, 'posterPath': '/AvvZq6VPyu0oGzejkyOSCTE6bfk.jpg'}, {'airDate': None, 'episodeCount': 10, 'id': 463821, 'name': 'Series 20', 'overview': '', 'seasonNumber': 20, 'posterPath': None}]
ERROR 2025-07-29 12:15:36,033 services 16106 6196359168 Failed to sync TV show 63404: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:15:36,035 services 16106 6196359168 Updated media request: 165
ERROR 2025-07-29 12:15:36,171 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
WARNING 2025-07-29 12:15:36,172 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': None, 'episodeCount': 63, 'id': 147409, 'name': 'Specials', 'overview': '', 'seasonNumber': 0, 'posterPath': '/k6qEnQ8xyQnFvTcULDlrzETQRgs.jpg'}, {'airDate': '2009-09-23', 'episodeCount': 24, 'id': 3751, 'name': 'Season 1', 'overview': 'Modern Family takes a refreshing and funny view of what it means to raise a family in this hectic day and age.  Multi-cultural relationships, adoption, and same-sex marriage are just a few of the timely issues faced by the show’s three wildly-diverse broods.  No matter the size or shape, family always comes first in this hilariously “modern” look at life, love, and laughter.', 'seasonNumber': 1, 'posterPath': '/i1KhQoI391KaEA5fKArrzoTvNDk.jpg'}, {'airDate': '2010-09-22', 'episodeCount': 24, 'id': 3752, 'name': 'Season 2', 'overview': 'While fledgling fathers Cameron and Mitchell struggle with learning the ropes of parenthood, long-time parents Claire and Phil try to keep the spice in their marriage amid the chaos of raising three challenging children. Meanwhile, family patriarch, Jay, has more than his hands full with his sexy, spirited wife, Gloria, and her sensitive son.', 'seasonNumber': 2, 'posterPath': '/yvBc8av9K1g5QRtBDnP5xY69jb4.jpg'}, {'airDate': '2011-09-21', 'episodeCount': 24, 'id': 3753, 'name': 'Season 3', 'overview': 'As the extended Pritchett-Dunphy clan faces an uproariously unpredictable array of family vacations, holiday hassles, troublesome in-laws, and surprising secrets, they still somehow manage to thrive together as one big, loving family a even as they drive each other absolutely insane!', 'seasonNumber': 3, 'posterPath': '/a4EJOG8VOV02veUIYtu4lX6FVdr.jpg'}, {'airDate': '2012-09-26', 'episodeCount': 24, 'id': 3755, 'name': 'Season 4', 'overview': "With Jay and Gloria's baby on the way and Haley going off to college, the entire Pritchett-Dunphy clan faces some major surprises as they bicker and bond over house-flipping headaches, unwanted play dates, and everything from hot-tempered hormones to in utero karaoke.", 'seasonNumber': 4, 'posterPath': '/pGXlhxw31fPJ6PwvzZdIRgaSHc2.jpg'}, {'airDate': '2013-09-25', 'episodeCount': 24, 'id': 3756, 'name': 'Season 5', 'overview': 'Wedding bells are ringing in season five of Modern Family. As Cam and Mitchell bicker over plans for their big day, the rest of the family has its hands full adapting to new jobs, new schools, and a new male nanny.  There are babysitting disasters, an anniversary to celebrate, misguided male bonding, and everything from high-stakes poker to high-maintenance in-laws.', 'seasonNumber': 5, 'posterPath': '/sJ9PqGDvGIOwJfSle62yGGieZC1.jpg'}, {'airDate': '2014-09-24', 'episodeCount': 24, 'id': 62023, 'name': 'Season 6', 'overview': "The honeymoon is over, but the laughs continue in season six of Modern Family. As freshly hitched Cam and Mitch acclimate to the realities of wedded bliss, Phil and Claire find their marriage stressed by annoying neighbors, Thanksgiving dinner gone awry and Claire's online snooping. Meanwhile, a spy-camera drone wreaks havoc in Jay and Gloria's backyard and a close call on the highway leads to amusing changes in various members of the Pritchett-Dunphy clan.", 'seasonNumber': 6, 'posterPath': '/5cUUBx6iUrWFvJ8BmP2d4SATy1G.jpg'}, {'airDate': '2015-09-23', 'episodeCount': 22, 'id': 68804, 'name': 'Season 7', 'overview': 'Enjoy a new chapter of love and laughter with the Seventh Season of the show. Quackery rules the roost when Phil adopts a trio of orphan ducklings and the rebellious Dunphy kids spread their wings. Meanwhile, Cam and Mitchell face financial hurdles and wild frat boys, Jay and Gloria 1farm2 out Joe2s preschool education and Manny leaps into the dating game. But will the big question finally be answered: are Haley and Andy destined to be together.', 'seasonNumber': 7, 'posterPath': '/825aF6sf43gIyPsX0oeNNhqMzuH.jpg'}, {'airDate': '2016-09-21', 'episodeCount': 22, 'id': 78515, 'name': 'Season 8', 'overview': "The household hilarity continues as the Dunphy clan is wrapping up a Big Apple adventure. Back at home, Claire struggles to keep order at Pritchett's Closets while Phil and Jay go into business together. Gloria's hot sauce business heats up, as Cam and Mitchell deal with their maturing tween, Lily. Meanwhile, Manny and Luke fumble through their senior year of high school.", 'seasonNumber': 8, 'posterPath': '/coOmsK9sWpScfLDlRXQ2xUJdzZ8.jpg'}, {'airDate': '2017-09-27', 'episodeCount': 22, 'id': 91749, 'name': 'Season 9', 'overview': "The outrageous ninth season revolves around the blended Pritchett-Dunphy-Tucker clan, headed by Jay Pritchett. Jay and his vivacious second wife Gloria are raising their young son Joe and Gloria's college bound son Manny. Meanwhile, Jay's grown daughter Claire and her husband Phil are becoming empty nesters, while Clare's brother Mitchell and his husband Cameron fumble through nurturing their gifted daughter Lily.", 'seasonNumber': 9, 'posterPath': '/gLAcu4VPCAb90oJvJ4nUJc5ZBQi.jpg'}, {'airDate': '2018-09-26', 'episodeCount': 22, 'id': 108448, 'name': 'Season 10', 'overview': "As the tenth season kicks off, Jay's slated to be grand marshal in a Fourth of July parade, prompting the Pritchett-Dunphy-Tuckers to set off a few too many fireworks. Phil stumbles into a new career opportunity, while Claire grapples with her potentially changing role in the family. Meanwhile, Gloria obsesses over Manny's relationship and Joe's extracurricular activities. The extended family deals with death, but they also evolve, with more laughs than ever.", 'seasonNumber': 10, 'posterPath': '/mXYC6CZ3uhGMSke1ysxp1elTmDl.jpg'}, {'airDate': '2019-09-25', 'episodeCount': 18, 'id': 127046, 'name': 'Season 11', 'overview': 'Jay and Gloria are navigating life with their youngest son, Joe, while Manny has headed off to college to explore the world on his own terms. Meanwhile, Claire and Phil have officially lost their status as empty-nesters when Haley started her own family and moved back home with her new husband, Dylan, and a set of twins. Luke, is now looking to his next move; and Alex is learning how to balance life outside of academia. Then there’s Mitchell and Cameron, who are still working to understand their gifted teenage daughter, Lily, and juggle busy careers.', 'seasonNumber': 11, 'posterPath': '/sMIhyJw2s1PRS8S7UtVnQrHAlNB.jpg'}]
ERROR 2025-07-29 12:15:36,172 services 16106 6196359168 Failed to sync TV show 1421: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:15:36,173 services 16106 6196359168 Updated media request: 164
ERROR 2025-07-29 12:15:36,355 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
WARNING 2025-07-29 12:15:36,355 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-22', 'episodeCount': 10, 'id': 203126, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/dlCEYPkgKl3WHrn8bYkkzJVPlTZ.jpg'}, {'airDate': '2025-01-23', 'episodeCount': 10, 'id': 422475, 'name': 'Season 2', 'overview': 'The hunt for a CIA agent accused of leaking secrets puts Peter and Rose in the crosshairs of a ruthless intelligence broker and a deadly war criminal.', 'seasonNumber': 2, 'posterPath': '/4c5yUNcaff4W4aPrkXE6zr7papX.jpg'}, {'airDate': None, 'episodeCount': 0, 'id': 441556, 'name': 'Season 3', 'overview': '', 'seasonNumber': 3, 'posterPath': None}]
ERROR 2025-07-29 12:15:36,355 services 16106 6196359168 Failed to sync TV show 129552: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:15:36,356 services 16106 6196359168 Updated media request: 163
ERROR 2025-07-29 12:15:36,429 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
WARNING 2025-07-29 12:15:36,429 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2024-04-20', 'episodeCount': 5, 'id': 381429, 'name': 'Season 1', 'overview': '', 'seasonNumber': 1, 'posterPath': '/hmNbB68i1zQ7xq7MY82eBujziYF.jpg'}, {'airDate': None, 'episodeCount': 6, 'id': 461441, 'name': 'Season 2', 'overview': '', 'seasonNumber': 2, 'posterPath': None}]
ERROR 2025-07-29 12:15:36,429 services 16106 6196359168 Failed to sync TV show 247704: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:15:36,430 services 16106 6196359168 Updated media request: 162
ERROR 2025-07-29 12:15:36,583 services 16106 6196359168 Unexpected error in async wrapper: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
WARNING 2025-07-29 12:15:36,583 services 16106 6196359168 Async TV details request failed, falling back to requests: Unexpected error: Field "seasons" of type list[Season] in TVDetails has invalid value [{'airDate': '2023-03-27', 'episodeCount': 15, 'id': 349382, 'name': 'Specials', 'overview': 'The cast of Yellowjackets breaks down the episodes of season 2', 'seasonNumber': 0, 'posterPath': '/boCY37Jx5w5EqyqKzCcLn6C90lN.jpg'}, {'airDate': '2021-11-14', 'episodeCount': 10, 'id': 178264, 'name': 'Season 1', 'overview': 'A thriving high school girls soccer squad is left stranded in the northern wilderness after a plane crash and descend into savage clans, where survival becomes even more important than the team.', 'seasonNumber': 1, 'posterPath': '/XtnjzjjFAnmTEiDk4xu7diCvMF.jpg'}, {'airDate': '2023-03-26', 'episodeCount': 9, 'id': 304399, 'name': 'Season 2', 'overview': 'The Yellowjackets barely made it through summer in the woods, but now as winter begins to bite, hunger and desperation threatens to turn into full-on psychosis. Meanwhile, twenty-five years later, each survivor must ask themselves—is the darkness coming for them, or is it coming from them?', 'seasonNumber': 2, 'posterPath': '/o4Ql7oU2W3U8RyeDoSREDdKvTlq.jpg'}, {'airDate': '2025-02-16', 'episodeCount': 10, 'id': 429246, 'name': 'Season 3', 'overview': 'In season three, as summer arrives, the Yellowjackets face a fragile victory—the brutal winter that nearly claimed them is finally behind them, but distrust in leadership and tension within the team jeopardize their chances of being rescued. In the present, long-buried secrets from their pasts begin to surface. As the women fight to keep their lives from unraveling, they must confront a chilling question: who are they really, and what dark truths are they hiding from each other and themselves?', 'seasonNumber': 3, 'posterPath': '/xRnGrn7Z7SC0KIBodocoU1QgDZF.jpg'}, {'airDate': None, 'episodeCount': 1, 'id': 457465, 'name': 'Season 4', 'overview': '', 'seasonNumber': 4, 'posterPath': None}]
ERROR 2025-07-29 12:15:36,583 services 16106 6196359168 Failed to sync TV show 117488: 'OverseerrAPIClient' object has no attribute 'session'
INFO 2025-07-29 12:15:36,585 services 16106 6196359168 Updated media request: 161
INFO 2025-07-29 12:15:36,673 services 16106 6196359168 Updated TV show: Based on a True Story
INFO 2025-07-29 12:15:36,678 services 16106 6196359168 Updated media request: 160
INFO 2025-07-29 12:15:36,881 services 16106 6196359168 Updated TV show: The White Lotus
INFO 2025-07-29 12:15:36,887 services 16106 6196359168 Successfully synced 7 TV requests
INFO 2025-07-29 12:15:36,887 views 16106 6196359168 Manual data sync completed successfully
INFO 2025-07-29 12:17:27,565 services 19029 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:17:37,942 services 19153 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:17:37,942 services 19153 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:17:38,011 services 19153 8539021056 Updated media request: 166
INFO 2025-07-29 12:17:38,148 services 19153 8539021056 Created new TV show: Taskmaster
INFO 2025-07-29 12:17:38,150 services 19153 8539021056 Created season 0 for Taskmaster
INFO 2025-07-29 12:17:38,152 services 19153 8539021056 Created season 1 for Taskmaster
INFO 2025-07-29 12:17:38,154 services 19153 8539021056 Created season 2 for Taskmaster
INFO 2025-07-29 12:17:38,155 services 19153 8539021056 Created season 3 for Taskmaster
INFO 2025-07-29 12:17:38,157 services 19153 8539021056 Created season 4 for Taskmaster
INFO 2025-07-29 12:17:38,157 services 19153 8539021056 Created season 5 for Taskmaster
INFO 2025-07-29 12:17:38,158 services 19153 8539021056 Created season 6 for Taskmaster
INFO 2025-07-29 12:17:38,160 services 19153 8539021056 Created season 7 for Taskmaster
INFO 2025-07-29 12:17:38,161 services 19153 8539021056 Created season 8 for Taskmaster
INFO 2025-07-29 12:17:38,162 services 19153 8539021056 Created season 9 for Taskmaster
INFO 2025-07-29 12:17:38,163 services 19153 8539021056 Created season 10 for Taskmaster
INFO 2025-07-29 12:17:38,164 services 19153 8539021056 Created season 11 for Taskmaster
INFO 2025-07-29 12:17:38,167 services 19153 8539021056 Created season 12 for Taskmaster
INFO 2025-07-29 12:17:38,168 services 19153 8539021056 Created season 13 for Taskmaster
INFO 2025-07-29 12:17:38,169 services 19153 8539021056 Created season 14 for Taskmaster
INFO 2025-07-29 12:17:38,171 services 19153 8539021056 Created season 15 for Taskmaster
INFO 2025-07-29 12:17:38,172 services 19153 8539021056 Created season 16 for Taskmaster
INFO 2025-07-29 12:17:38,173 services 19153 8539021056 Created season 17 for Taskmaster
INFO 2025-07-29 12:17:38,174 services 19153 8539021056 Created season 18 for Taskmaster
INFO 2025-07-29 12:17:38,175 services 19153 8539021056 Created season 19 for Taskmaster
ERROR 2025-07-29 12:17:38,176 services 19153 8539021056 Failed to sync TV show 63404: NOT NULL constraint failed: media_requests_season.poster_path
INFO 2025-07-29 12:17:38,177 services 19153 8539021056 Updated media request: 165
INFO 2025-07-29 12:17:38,315 services 19153 8539021056 Created new TV show: Modern Family
INFO 2025-07-29 12:17:38,316 services 19153 8539021056 Created season 0 for Modern Family
INFO 2025-07-29 12:17:38,318 services 19153 8539021056 Created season 1 for Modern Family
INFO 2025-07-29 12:17:38,319 services 19153 8539021056 Created season 2 for Modern Family
INFO 2025-07-29 12:17:38,321 services 19153 8539021056 Created season 3 for Modern Family
INFO 2025-07-29 12:17:38,322 services 19153 8539021056 Created season 4 for Modern Family
INFO 2025-07-29 12:17:38,323 services 19153 8539021056 Created season 5 for Modern Family
INFO 2025-07-29 12:17:38,325 services 19153 8539021056 Created season 6 for Modern Family
INFO 2025-07-29 12:17:38,326 services 19153 8539021056 Created season 7 for Modern Family
INFO 2025-07-29 12:17:38,328 services 19153 8539021056 Created season 8 for Modern Family
INFO 2025-07-29 12:17:38,330 services 19153 8539021056 Created season 9 for Modern Family
INFO 2025-07-29 12:17:38,332 services 19153 8539021056 Created season 10 for Modern Family
INFO 2025-07-29 12:17:38,334 services 19153 8539021056 Created season 11 for Modern Family
INFO 2025-07-29 12:17:38,338 services 19153 8539021056 Updated media request: 164
INFO 2025-07-29 12:17:38,427 services 19153 8539021056 Created new TV show: The Night Agent
INFO 2025-07-29 12:17:38,428 services 19153 8539021056 Created season 1 for The Night Agent
INFO 2025-07-29 12:17:38,429 services 19153 8539021056 Created season 2 for The Night Agent
ERROR 2025-07-29 12:17:38,430 services 19153 8539021056 Failed to sync TV show 129552: NOT NULL constraint failed: media_requests_season.poster_path
INFO 2025-07-29 12:17:38,431 services 19153 8539021056 Updated media request: 163
INFO 2025-07-29 12:17:38,505 services 19153 8539021056 Created new TV show: Red Eye
INFO 2025-07-29 12:17:38,507 services 19153 8539021056 Created season 1 for Red Eye
ERROR 2025-07-29 12:17:38,508 services 19153 8539021056 Failed to sync TV show 247704: NOT NULL constraint failed: media_requests_season.poster_path
INFO 2025-07-29 12:17:38,509 services 19153 8539021056 Updated media request: 162
INFO 2025-07-29 12:17:38,603 services 19153 8539021056 Created new TV show: Yellowjackets
INFO 2025-07-29 12:17:38,605 services 19153 8539021056 Created season 0 for Yellowjackets
INFO 2025-07-29 12:17:38,606 services 19153 8539021056 Created season 1 for Yellowjackets
INFO 2025-07-29 12:17:38,608 services 19153 8539021056 Created season 2 for Yellowjackets
INFO 2025-07-29 12:17:38,609 services 19153 8539021056 Created season 3 for Yellowjackets
ERROR 2025-07-29 12:17:38,610 services 19153 8539021056 Failed to sync TV show 117488: NOT NULL constraint failed: media_requests_season.poster_path
INFO 2025-07-29 12:17:38,611 services 19153 8539021056 Updated media request: 161
INFO 2025-07-29 12:17:38,695 services 19153 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:17:38,701 services 19153 8539021056 Updated media request: 160
INFO 2025-07-29 12:17:38,789 services 19153 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:17:38,795 services 19153 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:19:19,250 services 20340 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:19:19,250 services 20340 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:19:19,312 services 20340 8539021056 Updated media request: 166
INFO 2025-07-29 12:19:19,442 services 20340 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:19:19,464 services 20340 8539021056 Created season 20 for Taskmaster
INFO 2025-07-29 12:19:19,467 services 20340 8539021056 Updated media request: 165
INFO 2025-07-29 12:19:19,631 services 20340 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:19:19,653 services 20340 8539021056 Updated media request: 164
INFO 2025-07-29 12:19:19,749 services 20340 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:19:19,753 services 20340 8539021056 Created season 3 for The Night Agent
INFO 2025-07-29 12:19:19,756 services 20340 8539021056 Updated media request: 163
INFO 2025-07-29 12:19:19,830 services 20340 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:19:19,833 services 20340 8539021056 Created season 2 for Red Eye
INFO 2025-07-29 12:19:19,836 services 20340 8539021056 Updated media request: 162
INFO 2025-07-29 12:19:19,935 services 20340 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:19:19,942 services 20340 8539021056 Created season 4 for Yellowjackets
INFO 2025-07-29 12:19:19,945 services 20340 8539021056 Updated media request: 161
INFO 2025-07-29 12:19:20,029 services 20340 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:19:20,034 services 20340 8539021056 Updated media request: 160
INFO 2025-07-29 12:19:20,142 services 20340 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:19:20,148 services 20340 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:19:30,681 services 20491 8539021056 Using requests library for API calls (fallback)
ERROR 2025-07-29 12:19:30,681 services 20491 8539021056 Overseerr API request failed: Connection error
INFO 2025-07-29 12:19:30,682 services 20491 8539021056 Using requests library for API calls (fallback)
INFO 2025-07-29 12:19:30,682 services 20491 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:19:30,683 services 20491 8539021056 Created new media request: 1001
INFO 2025-07-29 12:19:30,685 services 20491 8539021056 Created new TV show: Test Show
INFO 2025-07-29 12:19:30,686 services 20491 8539021056 Successfully synced 1 TV requests
INFO 2025-07-29 12:19:36,041 services 19765 6167064576 Using python-overseerr library for API calls
INFO 2025-07-29 12:19:36,041 services 19765 6167064576 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:19:36,114 services 19765 6167064576 Updated media request: 166
INFO 2025-07-29 12:19:36,248 services 19765 6167064576 Updated TV show: Taskmaster
INFO 2025-07-29 12:19:36,275 services 19765 6167064576 Updated media request: 165
INFO 2025-07-29 12:19:36,403 services 19765 6167064576 Updated TV show: Modern Family
INFO 2025-07-29 12:19:36,416 services 19765 6167064576 Updated media request: 164
INFO 2025-07-29 12:19:36,506 services 19765 6167064576 Updated TV show: The Night Agent
INFO 2025-07-29 12:19:36,512 services 19765 6167064576 Updated media request: 163
INFO 2025-07-29 12:19:36,587 services 19765 6167064576 Updated TV show: Red Eye
INFO 2025-07-29 12:19:36,592 services 19765 6167064576 Updated media request: 162
INFO 2025-07-29 12:19:36,687 services 19765 6167064576 Updated TV show: Yellowjackets
INFO 2025-07-29 12:19:36,696 services 19765 6167064576 Updated media request: 161
INFO 2025-07-29 12:19:36,778 services 19765 6167064576 Updated TV show: Based on a True Story
INFO 2025-07-29 12:19:36,783 services 19765 6167064576 Updated media request: 160
INFO 2025-07-29 12:19:36,880 services 19765 6167064576 Updated TV show: The White Lotus
INFO 2025-07-29 12:19:36,886 services 19765 6167064576 Successfully synced 7 TV requests
INFO 2025-07-29 12:19:36,886 views 19765 6167064576 Manual data sync completed successfully
INFO 2025-07-29 12:20:07,293 services 19765 6167064576 Using python-overseerr library for API calls
INFO 2025-07-29 12:20:07,293 services 19765 6167064576 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:20:07,379 services 19765 6167064576 Updated media request: 166
INFO 2025-07-29 12:20:07,510 services 19765 6167064576 Updated TV show: Taskmaster
INFO 2025-07-29 12:20:07,531 services 19765 6167064576 Updated media request: 165
INFO 2025-07-29 12:20:07,671 services 19765 6167064576 Updated TV show: Modern Family
INFO 2025-07-29 12:20:07,681 services 19765 6167064576 Updated media request: 164
INFO 2025-07-29 12:20:07,782 services 19765 6167064576 Updated TV show: The Night Agent
INFO 2025-07-29 12:20:07,786 services 19765 6167064576 Updated media request: 163
INFO 2025-07-29 12:20:07,854 services 19765 6167064576 Updated TV show: Red Eye
INFO 2025-07-29 12:20:07,861 services 19765 6167064576 Updated media request: 162
INFO 2025-07-29 12:20:07,976 services 19765 6167064576 Updated TV show: Yellowjackets
INFO 2025-07-29 12:20:07,990 services 19765 6167064576 Updated media request: 161
INFO 2025-07-29 12:20:08,078 services 19765 6167064576 Updated TV show: Based on a True Story
INFO 2025-07-29 12:20:08,082 services 19765 6167064576 Updated media request: 160
INFO 2025-07-29 12:20:08,165 services 19765 6167064576 Updated TV show: The White Lotus
INFO 2025-07-29 12:20:08,173 services 19765 6167064576 Successfully synced 7 TV requests
INFO 2025-07-29 12:20:08,173 views 19765 6167064576 Manual data sync completed successfully
INFO 2025-07-29 12:21:51,677 services 22112 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:22:07,255 services 22365 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:22:07,255 services 22365 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:22:07,319 services 22365 8539021056 Updated media request: 166
INFO 2025-07-29 12:22:08,180 services 22365 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:22:08,192 services 22365 8539021056 Created episode 1 for Taskmaster - Season 0
INFO 2025-07-29 12:22:08,194 services 22365 8539021056 Created episode 2 for Taskmaster - Season 0
INFO 2025-07-29 12:22:08,196 services 22365 8539021056 Created episode 3 for Taskmaster - Season 0
INFO 2025-07-29 12:22:08,197 services 22365 8539021056 Created episode 4 for Taskmaster - Season 0
INFO 2025-07-29 12:22:08,199 services 22365 8539021056 Created episode 5 for Taskmaster - Season 0
INFO 2025-07-29 12:22:08,202 services 22365 8539021056 Created episode 1 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,203 services 22365 8539021056 Created episode 2 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,205 services 22365 8539021056 Created episode 3 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,206 services 22365 8539021056 Created episode 4 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,208 services 22365 8539021056 Created episode 5 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,209 services 22365 8539021056 Created episode 6 for Taskmaster - Season 1
INFO 2025-07-29 12:22:08,212 services 22365 8539021056 Created episode 1 for Taskmaster - Season 2
INFO 2025-07-29 12:22:08,213 services 22365 8539021056 Created episode 2 for Taskmaster - Season 2
INFO 2025-07-29 12:22:08,215 services 22365 8539021056 Created episode 3 for Taskmaster - Season 2
INFO 2025-07-29 12:22:08,216 services 22365 8539021056 Created episode 4 for Taskmaster - Season 2
INFO 2025-07-29 12:22:08,218 services 22365 8539021056 Created episode 5 for Taskmaster - Season 2
INFO 2025-07-29 12:22:08,220 services 22365 8539021056 Created episode 1 for Taskmaster - Season 3
INFO 2025-07-29 12:22:08,221 services 22365 8539021056 Created episode 2 for Taskmaster - Season 3
INFO 2025-07-29 12:22:08,223 services 22365 8539021056 Created episode 3 for Taskmaster - Season 3
INFO 2025-07-29 12:22:08,224 services 22365 8539021056 Created episode 4 for Taskmaster - Season 3
INFO 2025-07-29 12:22:08,225 services 22365 8539021056 Created episode 5 for Taskmaster - Season 3
INFO 2025-07-29 12:22:08,228 services 22365 8539021056 Created episode 1 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,229 services 22365 8539021056 Created episode 2 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,230 services 22365 8539021056 Created episode 3 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,232 services 22365 8539021056 Created episode 4 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,233 services 22365 8539021056 Created episode 5 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,234 services 22365 8539021056 Created episode 6 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,236 services 22365 8539021056 Created episode 7 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,237 services 22365 8539021056 Created episode 8 for Taskmaster - Season 4
INFO 2025-07-29 12:22:08,239 services 22365 8539021056 Created episode 1 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,240 services 22365 8539021056 Created episode 2 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,241 services 22365 8539021056 Created episode 3 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,242 services 22365 8539021056 Created episode 4 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,243 services 22365 8539021056 Created episode 5 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,244 services 22365 8539021056 Created episode 6 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,245 services 22365 8539021056 Created episode 7 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,246 services 22365 8539021056 Created episode 8 for Taskmaster - Season 5
INFO 2025-07-29 12:22:08,248 services 22365 8539021056 Created episode 1 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,249 services 22365 8539021056 Created episode 2 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,251 services 22365 8539021056 Created episode 3 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,252 services 22365 8539021056 Created episode 4 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,253 services 22365 8539021056 Created episode 5 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,254 services 22365 8539021056 Created episode 6 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,254 services 22365 8539021056 Created episode 7 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,256 services 22365 8539021056 Created episode 8 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,257 services 22365 8539021056 Created episode 9 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,257 services 22365 8539021056 Created episode 10 for Taskmaster - Season 6
INFO 2025-07-29 12:22:08,260 services 22365 8539021056 Created episode 1 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,261 services 22365 8539021056 Created episode 2 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,262 services 22365 8539021056 Created episode 3 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,263 services 22365 8539021056 Created episode 4 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,264 services 22365 8539021056 Created episode 5 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,265 services 22365 8539021056 Created episode 6 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,267 services 22365 8539021056 Created episode 7 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,268 services 22365 8539021056 Created episode 8 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,269 services 22365 8539021056 Created episode 9 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,270 services 22365 8539021056 Created episode 10 for Taskmaster - Season 7
INFO 2025-07-29 12:22:08,272 services 22365 8539021056 Created episode 1 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,274 services 22365 8539021056 Created episode 2 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,275 services 22365 8539021056 Created episode 3 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,276 services 22365 8539021056 Created episode 4 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,277 services 22365 8539021056 Created episode 5 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,278 services 22365 8539021056 Created episode 6 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,279 services 22365 8539021056 Created episode 7 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,280 services 22365 8539021056 Created episode 8 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,281 services 22365 8539021056 Created episode 9 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,282 services 22365 8539021056 Created episode 10 for Taskmaster - Season 8
INFO 2025-07-29 12:22:08,284 services 22365 8539021056 Created episode 1 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,285 services 22365 8539021056 Created episode 2 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,287 services 22365 8539021056 Created episode 3 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,287 services 22365 8539021056 Created episode 4 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,288 services 22365 8539021056 Created episode 5 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,289 services 22365 8539021056 Created episode 6 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,290 services 22365 8539021056 Created episode 7 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,291 services 22365 8539021056 Created episode 8 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,292 services 22365 8539021056 Created episode 9 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,293 services 22365 8539021056 Created episode 10 for Taskmaster - Season 9
INFO 2025-07-29 12:22:08,295 services 22365 8539021056 Created episode 1 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,295 services 22365 8539021056 Created episode 2 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,296 services 22365 8539021056 Created episode 3 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,297 services 22365 8539021056 Created episode 4 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,298 services 22365 8539021056 Created episode 5 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,299 services 22365 8539021056 Created episode 6 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,300 services 22365 8539021056 Created episode 7 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,301 services 22365 8539021056 Created episode 8 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,303 services 22365 8539021056 Created episode 9 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,303 services 22365 8539021056 Created episode 10 for Taskmaster - Season 10
INFO 2025-07-29 12:22:08,305 services 22365 8539021056 Created episode 1 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,306 services 22365 8539021056 Created episode 2 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,308 services 22365 8539021056 Created episode 3 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,308 services 22365 8539021056 Created episode 4 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,309 services 22365 8539021056 Created episode 5 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,310 services 22365 8539021056 Created episode 6 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,311 services 22365 8539021056 Created episode 7 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,312 services 22365 8539021056 Created episode 8 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,313 services 22365 8539021056 Created episode 9 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,313 services 22365 8539021056 Created episode 10 for Taskmaster - Season 11
INFO 2025-07-29 12:22:08,316 services 22365 8539021056 Created episode 1 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,317 services 22365 8539021056 Created episode 2 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,318 services 22365 8539021056 Created episode 3 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,318 services 22365 8539021056 Created episode 4 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,319 services 22365 8539021056 Created episode 5 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,320 services 22365 8539021056 Created episode 6 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,321 services 22365 8539021056 Created episode 7 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,322 services 22365 8539021056 Created episode 8 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,323 services 22365 8539021056 Created episode 9 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,324 services 22365 8539021056 Created episode 10 for Taskmaster - Season 12
INFO 2025-07-29 12:22:08,326 services 22365 8539021056 Created episode 1 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,326 services 22365 8539021056 Created episode 2 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,327 services 22365 8539021056 Created episode 3 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,328 services 22365 8539021056 Created episode 4 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,329 services 22365 8539021056 Created episode 5 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,330 services 22365 8539021056 Created episode 6 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,331 services 22365 8539021056 Created episode 7 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,332 services 22365 8539021056 Created episode 8 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,333 services 22365 8539021056 Created episode 9 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,334 services 22365 8539021056 Created episode 10 for Taskmaster - Season 13
INFO 2025-07-29 12:22:08,336 services 22365 8539021056 Created episode 1 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,337 services 22365 8539021056 Created episode 2 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,338 services 22365 8539021056 Created episode 3 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,339 services 22365 8539021056 Created episode 4 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,340 services 22365 8539021056 Created episode 5 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,341 services 22365 8539021056 Created episode 6 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,342 services 22365 8539021056 Created episode 7 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,344 services 22365 8539021056 Created episode 8 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,345 services 22365 8539021056 Created episode 9 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,346 services 22365 8539021056 Created episode 10 for Taskmaster - Season 14
INFO 2025-07-29 12:22:08,348 services 22365 8539021056 Created episode 1 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,349 services 22365 8539021056 Created episode 2 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,350 services 22365 8539021056 Created episode 3 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,350 services 22365 8539021056 Created episode 4 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,351 services 22365 8539021056 Created episode 5 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,352 services 22365 8539021056 Created episode 6 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,353 services 22365 8539021056 Created episode 7 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,354 services 22365 8539021056 Created episode 8 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,354 services 22365 8539021056 Created episode 9 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,355 services 22365 8539021056 Created episode 10 for Taskmaster - Season 15
INFO 2025-07-29 12:22:08,358 services 22365 8539021056 Created episode 1 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,359 services 22365 8539021056 Created episode 2 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,360 services 22365 8539021056 Created episode 3 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,361 services 22365 8539021056 Created episode 4 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,361 services 22365 8539021056 Created episode 5 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,362 services 22365 8539021056 Created episode 6 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,363 services 22365 8539021056 Created episode 7 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,364 services 22365 8539021056 Created episode 8 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,365 services 22365 8539021056 Created episode 9 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,367 services 22365 8539021056 Created episode 10 for Taskmaster - Season 16
INFO 2025-07-29 12:22:08,369 services 22365 8539021056 Created episode 1 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,370 services 22365 8539021056 Created episode 2 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,371 services 22365 8539021056 Created episode 3 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,372 services 22365 8539021056 Created episode 4 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,373 services 22365 8539021056 Created episode 5 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,374 services 22365 8539021056 Created episode 6 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,375 services 22365 8539021056 Created episode 7 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,376 services 22365 8539021056 Created episode 8 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,377 services 22365 8539021056 Created episode 9 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,378 services 22365 8539021056 Created episode 10 for Taskmaster - Season 17
INFO 2025-07-29 12:22:08,380 services 22365 8539021056 Created episode 1 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,380 services 22365 8539021056 Created episode 2 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,381 services 22365 8539021056 Created episode 3 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,382 services 22365 8539021056 Created episode 4 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,383 services 22365 8539021056 Created episode 5 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,384 services 22365 8539021056 Created episode 6 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,385 services 22365 8539021056 Created episode 7 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,386 services 22365 8539021056 Created episode 8 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,387 services 22365 8539021056 Created episode 9 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,388 services 22365 8539021056 Created episode 10 for Taskmaster - Season 18
INFO 2025-07-29 12:22:08,390 services 22365 8539021056 Created episode 1 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,391 services 22365 8539021056 Created episode 2 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,392 services 22365 8539021056 Created episode 3 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,392 services 22365 8539021056 Created episode 4 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,393 services 22365 8539021056 Created episode 5 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,394 services 22365 8539021056 Created episode 6 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,395 services 22365 8539021056 Created episode 7 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,396 services 22365 8539021056 Created episode 8 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,397 services 22365 8539021056 Created episode 9 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,398 services 22365 8539021056 Created episode 10 for Taskmaster - Season 19
INFO 2025-07-29 12:22:08,400 services 22365 8539021056 Created episode 1 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,400 services 22365 8539021056 Created episode 2 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,401 services 22365 8539021056 Created episode 3 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,402 services 22365 8539021056 Created episode 4 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,403 services 22365 8539021056 Created episode 5 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,404 services 22365 8539021056 Created episode 6 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,405 services 22365 8539021056 Created episode 7 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,406 services 22365 8539021056 Created episode 8 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,407 services 22365 8539021056 Created episode 9 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,408 services 22365 8539021056 Created episode 10 for Taskmaster - Season 20
INFO 2025-07-29 12:22:08,410 services 22365 8539021056 Updated media request: 165
INFO 2025-07-29 12:22:09,860 services 22365 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:22:09,866 services 22365 8539021056 Created episode 1 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,868 services 22365 8539021056 Created episode 2 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,870 services 22365 8539021056 Created episode 3 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,872 services 22365 8539021056 Created episode 4 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,875 services 22365 8539021056 Created episode 5 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,876 services 22365 8539021056 Created episode 6 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,879 services 22365 8539021056 Created episode 7 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,881 services 22365 8539021056 Created episode 8 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,882 services 22365 8539021056 Created episode 9 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,884 services 22365 8539021056 Created episode 10 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,886 services 22365 8539021056 Created episode 11 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,887 services 22365 8539021056 Created episode 12 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,888 services 22365 8539021056 Created episode 13 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,890 services 22365 8539021056 Created episode 14 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,891 services 22365 8539021056 Created episode 15 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,893 services 22365 8539021056 Created episode 16 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,895 services 22365 8539021056 Created episode 17 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,896 services 22365 8539021056 Created episode 18 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,897 services 22365 8539021056 Created episode 19 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,898 services 22365 8539021056 Created episode 20 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,900 services 22365 8539021056 Created episode 21 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,902 services 22365 8539021056 Created episode 22 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,903 services 22365 8539021056 Created episode 23 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,904 services 22365 8539021056 Created episode 24 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,905 services 22365 8539021056 Created episode 25 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,906 services 22365 8539021056 Created episode 26 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,908 services 22365 8539021056 Created episode 27 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,909 services 22365 8539021056 Created episode 28 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,910 services 22365 8539021056 Created episode 29 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,911 services 22365 8539021056 Created episode 30 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,912 services 22365 8539021056 Created episode 31 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,913 services 22365 8539021056 Created episode 32 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,915 services 22365 8539021056 Created episode 33 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,916 services 22365 8539021056 Created episode 34 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,917 services 22365 8539021056 Created episode 35 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,918 services 22365 8539021056 Created episode 36 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,919 services 22365 8539021056 Created episode 37 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,920 services 22365 8539021056 Created episode 38 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,921 services 22365 8539021056 Created episode 39 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,922 services 22365 8539021056 Created episode 40 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,923 services 22365 8539021056 Created episode 41 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,924 services 22365 8539021056 Created episode 42 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,925 services 22365 8539021056 Created episode 43 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,926 services 22365 8539021056 Created episode 44 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,928 services 22365 8539021056 Created episode 45 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,929 services 22365 8539021056 Created episode 46 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,929 services 22365 8539021056 Created episode 47 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,930 services 22365 8539021056 Created episode 48 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,932 services 22365 8539021056 Created episode 49 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,933 services 22365 8539021056 Created episode 50 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,934 services 22365 8539021056 Created episode 51 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,935 services 22365 8539021056 Created episode 52 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,936 services 22365 8539021056 Created episode 53 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,937 services 22365 8539021056 Created episode 54 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,938 services 22365 8539021056 Created episode 55 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,939 services 22365 8539021056 Created episode 56 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,940 services 22365 8539021056 Created episode 57 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,941 services 22365 8539021056 Created episode 58 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,942 services 22365 8539021056 Created episode 59 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,943 services 22365 8539021056 Created episode 60 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,944 services 22365 8539021056 Created episode 61 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,945 services 22365 8539021056 Created episode 62 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,946 services 22365 8539021056 Created episode 63 for Modern Family - Season 0
INFO 2025-07-29 12:22:09,949 services 22365 8539021056 Created episode 1 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,950 services 22365 8539021056 Created episode 2 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,951 services 22365 8539021056 Created episode 3 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,952 services 22365 8539021056 Created episode 4 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,953 services 22365 8539021056 Created episode 5 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,954 services 22365 8539021056 Created episode 6 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,954 services 22365 8539021056 Created episode 7 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,955 services 22365 8539021056 Created episode 8 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,956 services 22365 8539021056 Created episode 9 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,957 services 22365 8539021056 Created episode 10 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,958 services 22365 8539021056 Created episode 11 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,959 services 22365 8539021056 Created episode 12 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,960 services 22365 8539021056 Created episode 13 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,961 services 22365 8539021056 Created episode 14 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,962 services 22365 8539021056 Created episode 15 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,963 services 22365 8539021056 Created episode 16 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,965 services 22365 8539021056 Created episode 17 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,968 services 22365 8539021056 Created episode 18 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,969 services 22365 8539021056 Created episode 19 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,970 services 22365 8539021056 Created episode 20 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,971 services 22365 8539021056 Created episode 21 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,973 services 22365 8539021056 Created episode 22 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,974 services 22365 8539021056 Created episode 23 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,975 services 22365 8539021056 Created episode 24 for Modern Family - Season 1
INFO 2025-07-29 12:22:09,977 services 22365 8539021056 Created episode 1 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,978 services 22365 8539021056 Created episode 2 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,978 services 22365 8539021056 Created episode 3 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,979 services 22365 8539021056 Created episode 4 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,980 services 22365 8539021056 Created episode 5 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,981 services 22365 8539021056 Created episode 6 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,982 services 22365 8539021056 Created episode 7 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,983 services 22365 8539021056 Created episode 8 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,984 services 22365 8539021056 Created episode 9 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,985 services 22365 8539021056 Created episode 10 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,986 services 22365 8539021056 Created episode 11 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,987 services 22365 8539021056 Created episode 12 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,988 services 22365 8539021056 Created episode 13 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,989 services 22365 8539021056 Created episode 14 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,990 services 22365 8539021056 Created episode 15 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,991 services 22365 8539021056 Created episode 16 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,992 services 22365 8539021056 Created episode 17 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,993 services 22365 8539021056 Created episode 18 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,994 services 22365 8539021056 Created episode 19 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,994 services 22365 8539021056 Created episode 20 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,995 services 22365 8539021056 Created episode 21 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,996 services 22365 8539021056 Created episode 22 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,997 services 22365 8539021056 Created episode 23 for Modern Family - Season 2
INFO 2025-07-29 12:22:09,998 services 22365 8539021056 Created episode 24 for Modern Family - Season 2
INFO 2025-07-29 12:22:10,000 services 22365 8539021056 Created episode 1 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,002 services 22365 8539021056 Created episode 2 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,003 services 22365 8539021056 Created episode 3 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,004 services 22365 8539021056 Created episode 4 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,005 services 22365 8539021056 Created episode 5 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,006 services 22365 8539021056 Created episode 6 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,007 services 22365 8539021056 Created episode 7 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,008 services 22365 8539021056 Created episode 8 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,009 services 22365 8539021056 Created episode 9 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,010 services 22365 8539021056 Created episode 10 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,011 services 22365 8539021056 Created episode 11 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,011 services 22365 8539021056 Created episode 12 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,012 services 22365 8539021056 Created episode 13 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,013 services 22365 8539021056 Created episode 14 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,014 services 22365 8539021056 Created episode 15 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,015 services 22365 8539021056 Created episode 16 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,016 services 22365 8539021056 Created episode 17 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,017 services 22365 8539021056 Created episode 18 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,017 services 22365 8539021056 Created episode 19 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,018 services 22365 8539021056 Created episode 20 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,019 services 22365 8539021056 Created episode 21 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,020 services 22365 8539021056 Created episode 22 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,021 services 22365 8539021056 Created episode 23 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,021 services 22365 8539021056 Created episode 24 for Modern Family - Season 3
INFO 2025-07-29 12:22:10,024 services 22365 8539021056 Created episode 1 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,025 services 22365 8539021056 Created episode 2 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,026 services 22365 8539021056 Created episode 3 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,027 services 22365 8539021056 Created episode 4 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,028 services 22365 8539021056 Created episode 5 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,029 services 22365 8539021056 Created episode 6 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,030 services 22365 8539021056 Created episode 7 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,031 services 22365 8539021056 Created episode 8 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,032 services 22365 8539021056 Created episode 9 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,033 services 22365 8539021056 Created episode 10 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,034 services 22365 8539021056 Created episode 11 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,035 services 22365 8539021056 Created episode 12 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,036 services 22365 8539021056 Created episode 13 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,037 services 22365 8539021056 Created episode 14 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,038 services 22365 8539021056 Created episode 15 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,039 services 22365 8539021056 Created episode 16 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,040 services 22365 8539021056 Created episode 17 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,041 services 22365 8539021056 Created episode 18 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,042 services 22365 8539021056 Created episode 19 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,043 services 22365 8539021056 Created episode 20 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,045 services 22365 8539021056 Created episode 21 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,046 services 22365 8539021056 Created episode 22 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,047 services 22365 8539021056 Created episode 23 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,048 services 22365 8539021056 Created episode 24 for Modern Family - Season 4
INFO 2025-07-29 12:22:10,050 services 22365 8539021056 Created episode 1 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,051 services 22365 8539021056 Created episode 2 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,052 services 22365 8539021056 Created episode 3 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,053 services 22365 8539021056 Created episode 4 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,054 services 22365 8539021056 Created episode 5 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,055 services 22365 8539021056 Created episode 6 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,055 services 22365 8539021056 Created episode 7 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,057 services 22365 8539021056 Created episode 8 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,058 services 22365 8539021056 Created episode 9 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,059 services 22365 8539021056 Created episode 10 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,060 services 22365 8539021056 Created episode 11 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,061 services 22365 8539021056 Created episode 12 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,062 services 22365 8539021056 Created episode 13 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,063 services 22365 8539021056 Created episode 14 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,064 services 22365 8539021056 Created episode 15 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,066 services 22365 8539021056 Created episode 16 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,067 services 22365 8539021056 Created episode 17 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,068 services 22365 8539021056 Created episode 18 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,069 services 22365 8539021056 Created episode 19 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,070 services 22365 8539021056 Created episode 20 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,071 services 22365 8539021056 Created episode 21 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,072 services 22365 8539021056 Created episode 22 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,073 services 22365 8539021056 Created episode 23 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,074 services 22365 8539021056 Created episode 24 for Modern Family - Season 5
INFO 2025-07-29 12:22:10,076 services 22365 8539021056 Created episode 1 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,077 services 22365 8539021056 Created episode 2 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,078 services 22365 8539021056 Created episode 3 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,079 services 22365 8539021056 Created episode 4 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,080 services 22365 8539021056 Created episode 5 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,080 services 22365 8539021056 Created episode 6 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,081 services 22365 8539021056 Created episode 7 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,082 services 22365 8539021056 Created episode 8 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,083 services 22365 8539021056 Created episode 9 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,084 services 22365 8539021056 Created episode 10 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,085 services 22365 8539021056 Created episode 11 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,086 services 22365 8539021056 Created episode 12 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,087 services 22365 8539021056 Created episode 13 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,088 services 22365 8539021056 Created episode 14 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,089 services 22365 8539021056 Created episode 15 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,090 services 22365 8539021056 Created episode 16 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,091 services 22365 8539021056 Created episode 17 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,092 services 22365 8539021056 Created episode 18 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,093 services 22365 8539021056 Created episode 19 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,094 services 22365 8539021056 Created episode 20 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,095 services 22365 8539021056 Created episode 21 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,096 services 22365 8539021056 Created episode 22 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,097 services 22365 8539021056 Created episode 23 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,098 services 22365 8539021056 Created episode 24 for Modern Family - Season 6
INFO 2025-07-29 12:22:10,100 services 22365 8539021056 Created episode 1 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,101 services 22365 8539021056 Created episode 2 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,102 services 22365 8539021056 Created episode 3 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,103 services 22365 8539021056 Created episode 4 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,103 services 22365 8539021056 Created episode 5 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,104 services 22365 8539021056 Created episode 6 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,105 services 22365 8539021056 Created episode 7 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,107 services 22365 8539021056 Created episode 8 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,108 services 22365 8539021056 Created episode 9 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,108 services 22365 8539021056 Created episode 10 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,109 services 22365 8539021056 Created episode 11 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,110 services 22365 8539021056 Created episode 12 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,111 services 22365 8539021056 Created episode 13 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,111 services 22365 8539021056 Created episode 14 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,112 services 22365 8539021056 Created episode 15 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,113 services 22365 8539021056 Created episode 16 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,114 services 22365 8539021056 Created episode 17 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,115 services 22365 8539021056 Created episode 18 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,116 services 22365 8539021056 Created episode 19 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,117 services 22365 8539021056 Created episode 20 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,118 services 22365 8539021056 Created episode 21 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,118 services 22365 8539021056 Created episode 22 for Modern Family - Season 7
INFO 2025-07-29 12:22:10,120 services 22365 8539021056 Created episode 1 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,121 services 22365 8539021056 Created episode 2 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,122 services 22365 8539021056 Created episode 3 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,123 services 22365 8539021056 Created episode 4 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,124 services 22365 8539021056 Created episode 5 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,124 services 22365 8539021056 Created episode 6 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,125 services 22365 8539021056 Created episode 7 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,126 services 22365 8539021056 Created episode 8 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,127 services 22365 8539021056 Created episode 9 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,128 services 22365 8539021056 Created episode 10 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,129 services 22365 8539021056 Created episode 11 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,130 services 22365 8539021056 Created episode 12 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,131 services 22365 8539021056 Created episode 13 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,132 services 22365 8539021056 Created episode 14 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,133 services 22365 8539021056 Created episode 15 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,134 services 22365 8539021056 Created episode 16 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,135 services 22365 8539021056 Created episode 17 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,136 services 22365 8539021056 Created episode 18 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,137 services 22365 8539021056 Created episode 19 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,138 services 22365 8539021056 Created episode 20 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,139 services 22365 8539021056 Created episode 21 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,140 services 22365 8539021056 Created episode 22 for Modern Family - Season 8
INFO 2025-07-29 12:22:10,142 services 22365 8539021056 Created episode 1 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,143 services 22365 8539021056 Created episode 2 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,144 services 22365 8539021056 Created episode 3 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,145 services 22365 8539021056 Created episode 4 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,146 services 22365 8539021056 Created episode 5 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,147 services 22365 8539021056 Created episode 6 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,148 services 22365 8539021056 Created episode 7 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,149 services 22365 8539021056 Created episode 8 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,149 services 22365 8539021056 Created episode 9 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,150 services 22365 8539021056 Created episode 10 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,151 services 22365 8539021056 Created episode 11 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,152 services 22365 8539021056 Created episode 12 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,153 services 22365 8539021056 Created episode 13 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,153 services 22365 8539021056 Created episode 14 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,154 services 22365 8539021056 Created episode 15 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,155 services 22365 8539021056 Created episode 16 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,156 services 22365 8539021056 Created episode 17 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,157 services 22365 8539021056 Created episode 18 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,158 services 22365 8539021056 Created episode 19 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,159 services 22365 8539021056 Created episode 20 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,160 services 22365 8539021056 Created episode 21 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,161 services 22365 8539021056 Created episode 22 for Modern Family - Season 9
INFO 2025-07-29 12:22:10,163 services 22365 8539021056 Created episode 1 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,164 services 22365 8539021056 Created episode 2 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,165 services 22365 8539021056 Created episode 3 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,166 services 22365 8539021056 Created episode 4 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,167 services 22365 8539021056 Created episode 5 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,169 services 22365 8539021056 Created episode 6 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,170 services 22365 8539021056 Created episode 7 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,171 services 22365 8539021056 Created episode 8 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,171 services 22365 8539021056 Created episode 9 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,172 services 22365 8539021056 Created episode 10 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,173 services 22365 8539021056 Created episode 11 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,174 services 22365 8539021056 Created episode 12 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,175 services 22365 8539021056 Created episode 13 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,176 services 22365 8539021056 Created episode 14 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,177 services 22365 8539021056 Created episode 15 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,177 services 22365 8539021056 Created episode 16 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,178 services 22365 8539021056 Created episode 17 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,179 services 22365 8539021056 Created episode 18 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,180 services 22365 8539021056 Created episode 19 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,181 services 22365 8539021056 Created episode 20 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,182 services 22365 8539021056 Created episode 21 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,183 services 22365 8539021056 Created episode 22 for Modern Family - Season 10
INFO 2025-07-29 12:22:10,185 services 22365 8539021056 Created episode 1 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,186 services 22365 8539021056 Created episode 2 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,187 services 22365 8539021056 Created episode 3 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,188 services 22365 8539021056 Created episode 4 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,189 services 22365 8539021056 Created episode 5 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,190 services 22365 8539021056 Created episode 6 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,191 services 22365 8539021056 Created episode 7 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,192 services 22365 8539021056 Created episode 8 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,193 services 22365 8539021056 Created episode 9 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,194 services 22365 8539021056 Created episode 10 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,194 services 22365 8539021056 Created episode 11 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,195 services 22365 8539021056 Created episode 12 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,196 services 22365 8539021056 Created episode 13 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,197 services 22365 8539021056 Created episode 14 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,198 services 22365 8539021056 Created episode 15 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,199 services 22365 8539021056 Created episode 16 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,200 services 22365 8539021056 Created episode 17 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,201 services 22365 8539021056 Created episode 18 for Modern Family - Season 11
INFO 2025-07-29 12:22:10,204 services 22365 8539021056 Updated media request: 164
INFO 2025-07-29 12:22:10,806 services 22365 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:22:10,808 services 22365 8539021056 Created episode 1 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,809 services 22365 8539021056 Created episode 2 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,810 services 22365 8539021056 Created episode 3 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,811 services 22365 8539021056 Created episode 4 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,812 services 22365 8539021056 Created episode 5 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,813 services 22365 8539021056 Created episode 6 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,813 services 22365 8539021056 Created episode 7 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,814 services 22365 8539021056 Created episode 8 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,815 services 22365 8539021056 Created episode 9 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,816 services 22365 8539021056 Created episode 10 for The Night Agent - Season 1
INFO 2025-07-29 12:22:10,818 services 22365 8539021056 Created episode 1 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,819 services 22365 8539021056 Created episode 2 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,820 services 22365 8539021056 Created episode 3 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,821 services 22365 8539021056 Created episode 4 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,821 services 22365 8539021056 Created episode 5 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,822 services 22365 8539021056 Created episode 6 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,823 services 22365 8539021056 Created episode 7 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,824 services 22365 8539021056 Created episode 8 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,825 services 22365 8539021056 Created episode 9 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,826 services 22365 8539021056 Created episode 10 for The Night Agent - Season 2
INFO 2025-07-29 12:22:10,829 services 22365 8539021056 Updated media request: 163
INFO 2025-07-29 12:22:11,243 services 22365 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:22:11,248 services 22365 8539021056 Created episode 1 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,250 services 22365 8539021056 Created episode 2 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,252 services 22365 8539021056 Created episode 3 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,254 services 22365 8539021056 Created episode 4 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,255 services 22365 8539021056 Created episode 5 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,257 services 22365 8539021056 Created episode 6 for Red Eye - Season 1
INFO 2025-07-29 12:22:11,260 services 22365 8539021056 Created episode 1 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,262 services 22365 8539021056 Created episode 2 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,263 services 22365 8539021056 Created episode 3 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,265 services 22365 8539021056 Created episode 4 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,266 services 22365 8539021056 Created episode 5 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,268 services 22365 8539021056 Created episode 6 for Red Eye - Season 2
INFO 2025-07-29 12:22:11,270 services 22365 8539021056 Updated media request: 162
INFO 2025-07-29 12:22:11,790 services 22365 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:22:11,796 services 22365 8539021056 Created episode 1 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,798 services 22365 8539021056 Created episode 2 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,800 services 22365 8539021056 Created episode 3 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,803 services 22365 8539021056 Created episode 4 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,805 services 22365 8539021056 Created episode 5 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,807 services 22365 8539021056 Created episode 6 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,809 services 22365 8539021056 Created episode 7 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,811 services 22365 8539021056 Created episode 8 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,813 services 22365 8539021056 Created episode 9 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,815 services 22365 8539021056 Created episode 10 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,816 services 22365 8539021056 Created episode 11 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,818 services 22365 8539021056 Created episode 15 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,819 services 22365 8539021056 Created episode 16 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,821 services 22365 8539021056 Created episode 17 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,822 services 22365 8539021056 Created episode 18 for Yellowjackets - Season 0
INFO 2025-07-29 12:22:11,824 services 22365 8539021056 Created episode 1 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,826 services 22365 8539021056 Created episode 2 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,827 services 22365 8539021056 Created episode 3 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,828 services 22365 8539021056 Created episode 4 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,829 services 22365 8539021056 Created episode 5 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,830 services 22365 8539021056 Created episode 6 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,831 services 22365 8539021056 Created episode 7 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,833 services 22365 8539021056 Created episode 8 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,834 services 22365 8539021056 Created episode 9 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,835 services 22365 8539021056 Created episode 10 for Yellowjackets - Season 1
INFO 2025-07-29 12:22:11,838 services 22365 8539021056 Created episode 1 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,838 services 22365 8539021056 Created episode 2 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,839 services 22365 8539021056 Created episode 3 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,840 services 22365 8539021056 Created episode 4 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,841 services 22365 8539021056 Created episode 5 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,842 services 22365 8539021056 Created episode 6 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,843 services 22365 8539021056 Created episode 7 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,844 services 22365 8539021056 Created episode 8 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,846 services 22365 8539021056 Created episode 9 for Yellowjackets - Season 2
INFO 2025-07-29 12:22:11,848 services 22365 8539021056 Created episode 1 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,849 services 22365 8539021056 Created episode 2 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,850 services 22365 8539021056 Created episode 3 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,851 services 22365 8539021056 Created episode 4 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,852 services 22365 8539021056 Created episode 5 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,853 services 22365 8539021056 Created episode 6 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,854 services 22365 8539021056 Created episode 7 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,855 services 22365 8539021056 Created episode 8 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,856 services 22365 8539021056 Created episode 9 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,857 services 22365 8539021056 Created episode 10 for Yellowjackets - Season 3
INFO 2025-07-29 12:22:11,859 services 22365 8539021056 Created episode 1 for Yellowjackets - Season 4
INFO 2025-07-29 12:22:11,861 services 22365 8539021056 Updated media request: 161
INFO 2025-07-29 12:22:12,262 services 22365 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:22:12,267 services 22365 8539021056 Created episode 1 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,270 services 22365 8539021056 Created episode 2 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,272 services 22365 8539021056 Created episode 3 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,274 services 22365 8539021056 Created episode 4 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,275 services 22365 8539021056 Created episode 5 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,277 services 22365 8539021056 Created episode 6 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,279 services 22365 8539021056 Created episode 7 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,280 services 22365 8539021056 Created episode 8 for Based on a True Story - Season 1
INFO 2025-07-29 12:22:12,284 services 22365 8539021056 Created episode 1 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,286 services 22365 8539021056 Created episode 2 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,287 services 22365 8539021056 Created episode 3 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,288 services 22365 8539021056 Created episode 4 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,289 services 22365 8539021056 Created episode 5 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,291 services 22365 8539021056 Created episode 6 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,292 services 22365 8539021056 Created episode 7 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,293 services 22365 8539021056 Created episode 8 for Based on a True Story - Season 2
INFO 2025-07-29 12:22:12,295 services 22365 8539021056 Updated media request: 160
INFO 2025-07-29 12:22:12,576 services 22365 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:22:12,582 services 22365 8539021056 Created episode 1 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,584 services 22365 8539021056 Created episode 2 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,585 services 22365 8539021056 Created episode 3 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,587 services 22365 8539021056 Created episode 4 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,589 services 22365 8539021056 Created episode 5 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,590 services 22365 8539021056 Created episode 6 for The White Lotus - Season 1
INFO 2025-07-29 12:22:12,594 services 22365 8539021056 Created episode 1 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,596 services 22365 8539021056 Created episode 2 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,597 services 22365 8539021056 Created episode 3 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,599 services 22365 8539021056 Created episode 4 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,600 services 22365 8539021056 Created episode 5 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,601 services 22365 8539021056 Created episode 6 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,603 services 22365 8539021056 Created episode 7 for The White Lotus - Season 2
INFO 2025-07-29 12:22:12,605 services 22365 8539021056 Created episode 1 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,606 services 22365 8539021056 Created episode 2 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,608 services 22365 8539021056 Created episode 3 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,609 services 22365 8539021056 Created episode 4 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,610 services 22365 8539021056 Created episode 5 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,611 services 22365 8539021056 Created episode 6 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,612 services 22365 8539021056 Created episode 7 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,613 services 22365 8539021056 Created episode 8 for The White Lotus - Season 3
INFO 2025-07-29 12:22:12,615 services 22365 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:24:17,497 services 23904 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:24:30,547 services 24052 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:25:00,635 services 24401 8539021056 Using python-overseerr library for API calls
ERROR 2025-07-29 12:25:00,677 services 24401 8539021056 Overseerr API request failed: 405 Client Error: Method Not Allowed for url: http://*************:5055/api/v1/media/tv/63404
INFO 2025-07-29 12:25:14,313 services 24572 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:27:18,490 services 26035 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:27:38,072 services 26259 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:27:38,073 services 26259 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:27:38,138 services 26259 8539021056 Updated media request: 166
INFO 2025-07-29 12:27:38,978 services 26259 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:27:39,147 services 26259 8539021056 Updated media request: 165
INFO 2025-07-29 12:27:40,108 services 26259 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:27:40,344 services 26259 8539021056 Updated media request: 164
INFO 2025-07-29 12:27:40,612 services 26259 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:27:40,635 services 26259 8539021056 Updated media request: 163
INFO 2025-07-29 12:27:40,883 services 26259 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:27:40,902 services 26259 8539021056 Updated media request: 162
INFO 2025-07-29 12:27:41,285 services 26259 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:27:41,333 services 26259 8539021056 Updated media request: 161
INFO 2025-07-29 12:27:41,537 services 26259 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:27:41,558 services 26259 8539021056 Updated media request: 160
INFO 2025-07-29 12:27:41,811 services 26259 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:27:41,843 services 26259 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:31:04,195 services 28759 8539021056 Using python-overseerr library for API calls
WARNING 2025-07-29 12:31:04,346 services 28759 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:31:04,347 services 28759 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:31:56,835 services 29355 8539021056 Using python-overseerr library for API calls
WARNING 2025-07-29 12:31:56,994 services 29355 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:31:56,994 services 29355 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:21,345 services 28606 6161330176 Using python-overseerr library for API calls
INFO 2025-07-29 12:32:21,345 services 28606 6161330176 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:32:21,411 services 28606 6161330176 Updated media request: 166
WARNING 2025-07-29 12:32:21,537 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:21,538 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:22,729 services 28606 6161330176 Updated TV show: Taskmaster
INFO 2025-07-29 12:32:22,885 services 28606 6161330176 Updated media request: 165
WARNING 2025-07-29 12:32:23,019 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:23,019 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:23,530 services 28606 6161330176 Updated TV show: Modern Family
INFO 2025-07-29 12:32:23,790 services 28606 6161330176 Updated media request: 164
WARNING 2025-07-29 12:32:23,879 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:23,879 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:23,982 services 28606 6161330176 Updated TV show: The Night Agent
INFO 2025-07-29 12:32:24,007 services 28606 6161330176 Updated media request: 163
WARNING 2025-07-29 12:32:24,061 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:24,061 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:24,135 services 28606 6161330176 Updated TV show: Red Eye
INFO 2025-07-29 12:32:24,152 services 28606 6161330176 Updated media request: 162
WARNING 2025-07-29 12:32:24,239 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:24,239 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:24,411 services 28606 6161330176 Updated TV show: Yellowjackets
INFO 2025-07-29 12:32:24,479 services 28606 6161330176 Updated media request: 161
WARNING 2025-07-29 12:32:24,560 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:24,560 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:24,626 services 28606 6161330176 Updated TV show: Based on a True Story
INFO 2025-07-29 12:32:24,657 services 28606 6161330176 Updated media request: 160
WARNING 2025-07-29 12:32:24,746 services 28606 6161330176 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:24,746 services 28606 6161330176 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:24,860 services 28606 6161330176 Updated TV show: The White Lotus
INFO 2025-07-29 12:32:24,897 services 28606 6161330176 Successfully synced 7 TV requests
INFO 2025-07-29 12:32:24,897 views 28606 6161330176 Manual data sync completed successfully
INFO 2025-07-29 12:32:35,736 services 29827 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:32:35,737 services 29827 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:32:35,811 services 29827 8539021056 Updated media request: 166
WARNING 2025-07-29 12:32:35,970 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:35,971 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:37,138 services 29827 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:32:37,300 services 29827 8539021056 Updated media request: 165
WARNING 2025-07-29 12:32:37,475 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:37,475 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:38,155 services 29827 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:32:38,396 services 29827 8539021056 Updated media request: 164
WARNING 2025-07-29 12:32:38,505 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:38,505 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:38,663 services 29827 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:32:38,695 services 29827 8539021056 Updated media request: 163
WARNING 2025-07-29 12:32:38,780 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:38,780 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:38,874 services 29827 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:32:38,888 services 29827 8539021056 Updated media request: 162
WARNING 2025-07-29 12:32:39,011 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:39,011 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:39,193 services 29827 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:32:39,248 services 29827 8539021056 Updated media request: 161
WARNING 2025-07-29 12:32:39,348 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:39,348 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:39,432 services 29827 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:32:39,456 services 29827 8539021056 Updated media request: 160
WARNING 2025-07-29 12:32:39,559 services 29827 8539021056 Failed to initialize Plex client: PLEX_TOKEN is not configured
INFO 2025-07-29 12:32:39,559 services 29827 8539021056 Using Overseerr status for availability (Plex not available)
INFO 2025-07-29 12:32:39,694 services 29827 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:32:39,725 services 29827 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:33:23,820 services 30366 6188986368 Using python-overseerr library for API calls
INFO 2025-07-29 12:33:23,821 services 30366 6188986368 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:33:23,897 services 30366 6188986368 Updated media request: 166
INFO 2025-07-29 12:33:24,047 services 30366 6188986368 Checking Plex availability for rating key 63790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,098 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240063790
ERROR 2025-07-29 12:33:24,101 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240063790
INFO 2025-07-29 12:33:24,864 services 30366 6188986368 Updated TV show: Taskmaster
INFO 2025-07-29 12:33:25,094 services 30366 6188986368 Updated media request: 165
INFO 2025-07-29 12:33:25,254 services 30366 6188986368 Checking Plex availability for rating key 72947
ERROR 2025-07-29 12:33:25,300 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,300 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,301 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,302 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,302 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240072947
ERROR 2025-07-29 12:33:25,302 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240072947
INFO 2025-07-29 12:33:26,152 services 30366 6188986368 Updated TV show: Modern Family
INFO 2025-07-29 12:33:26,382 services 30366 6188986368 Updated media request: 164
INFO 2025-07-29 12:33:26,486 services 30366 6188986368 Checking Plex availability for rating key 65139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240065139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240065139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240065139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240065139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240065139
ERROR 2025-07-29 12:33:26,519 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240065139
INFO 2025-07-29 12:33:26,696 services 30366 6188986368 Updated TV show: The Night Agent
INFO 2025-07-29 12:33:26,718 services 30366 6188986368 Updated media request: 163
INFO 2025-07-29 12:33:26,814 services 30366 6188986368 Checking Plex availability for rating key 130288
ERROR 2025-07-29 12:33:26,842 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:32400130288
ERROR 2025-07-29 12:33:26,842 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:32400130288
ERROR 2025-07-29 12:33:26,843 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:32400130288
ERROR 2025-07-29 12:33:26,843 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:32400130288
INFO 2025-07-29 12:33:26,962 services 30366 6188986368 Updated TV show: Red Eye
INFO 2025-07-29 12:33:26,980 services 30366 6188986368 Updated media request: 162
INFO 2025-07-29 12:33:27,067 services 30366 6188986368 Checking Plex availability for rating key 77791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,099 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240077791
ERROR 2025-07-29 12:33:27,100 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240077791
INFO 2025-07-29 12:33:27,409 services 30366 6188986368 Updated TV show: Yellowjackets
INFO 2025-07-29 12:33:27,467 services 30366 6188986368 Updated media request: 161
INFO 2025-07-29 12:33:27,560 services 30366 6188986368 Checking Plex availability for rating key 67674
ERROR 2025-07-29 12:33:27,590 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240067674
ERROR 2025-07-29 12:33:27,590 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240067674
ERROR 2025-07-29 12:33:27,590 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240067674
ERROR 2025-07-29 12:33:27,590 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240067674
INFO 2025-07-29 12:33:27,699 services 30366 6188986368 Updated TV show: Based on a True Story
INFO 2025-07-29 12:33:27,717 services 30366 6188986368 Updated media request: 160
INFO 2025-07-29 12:33:27,809 services 30366 6188986368 Checking Plex availability for rating key 66668
ERROR 2025-07-29 12:33:27,839 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240066668
ERROR 2025-07-29 12:33:27,839 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240066668
ERROR 2025-07-29 12:33:27,840 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240066668
ERROR 2025-07-29 12:33:27,840 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240066668
ERROR 2025-07-29 12:33:27,840 services 30366 6188986368 Error fetching show from Plex: Failed to parse: http://*************:3240066668
ERROR 2025-07-29 12:33:27,840 services 30366 6188986368 Error getting available episodes: Error fetching show from Plex: Failed to parse: http://*************:3240066668
INFO 2025-07-29 12:33:28,004 services 30366 6188986368 Updated TV show: The White Lotus
INFO 2025-07-29 12:33:28,026 services 30366 6188986368 Successfully synced 7 TV requests
INFO 2025-07-29 12:33:28,026 views 30366 6188986368 Manual data sync completed successfully
INFO 2025-07-29 12:35:13,187 services 31870 6128234496 Using python-overseerr library for API calls
INFO 2025-07-29 12:35:13,187 services 31870 6128234496 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:35:13,257 services 31870 6128234496 Updated media request: 166
INFO 2025-07-29 12:35:13,394 services 31870 6128234496 Checking Plex availability for rating key 63790
INFO 2025-07-29 12:35:15,588 services 31870 6128234496 Updated TV show: Taskmaster
INFO 2025-07-29 12:35:15,774 services 31870 6128234496 Updated media request: 165
INFO 2025-07-29 12:35:15,947 services 31870 6128234496 Checking Plex availability for rating key 72947
INFO 2025-07-29 12:35:17,359 services 31870 6128234496 Updated TV show: Modern Family
INFO 2025-07-29 12:35:17,633 services 31870 6128234496 Updated media request: 164
INFO 2025-07-29 12:35:17,720 services 31870 6128234496 Checking Plex availability for rating key 65139
INFO 2025-07-29 12:35:18,063 services 31870 6128234496 Updated TV show: The Night Agent
INFO 2025-07-29 12:35:18,089 services 31870 6128234496 Updated media request: 163
INFO 2025-07-29 12:35:18,161 services 31870 6128234496 Checking Plex availability for rating key 130288
INFO 2025-07-29 12:35:18,348 services 31870 6128234496 Updated TV show: Red Eye
INFO 2025-07-29 12:35:18,363 services 31870 6128234496 Updated media request: 162
INFO 2025-07-29 12:35:18,469 services 31870 6128234496 Checking Plex availability for rating key 77791
INFO 2025-07-29 12:35:18,965 services 31870 6128234496 Updated TV show: Yellowjackets
INFO 2025-07-29 12:35:19,019 services 31870 6128234496 Updated media request: 161
INFO 2025-07-29 12:35:19,102 services 31870 6128234496 Checking Plex availability for rating key 67674
INFO 2025-07-29 12:35:19,338 services 31870 6128234496 Updated TV show: Based on a True Story
INFO 2025-07-29 12:35:19,358 services 31870 6128234496 Updated media request: 160
INFO 2025-07-29 12:35:19,433 services 31870 6128234496 Checking Plex availability for rating key 66668
INFO 2025-07-29 12:35:19,783 services 31870 6128234496 Updated TV show: The White Lotus
INFO 2025-07-29 12:35:19,810 services 31870 6128234496 Successfully synced 7 TV requests
INFO 2025-07-29 12:35:19,810 views 31870 6128234496 Manual data sync completed successfully
INFO 2025-07-29 12:35:48,888 services 32577 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:35:48,888 services 32577 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:35:48,973 services 32577 8539021056 Updated media request: 166
INFO 2025-07-29 12:35:49,108 services 32577 8539021056 Checking Plex availability for rating key 63790
INFO 2025-07-29 12:35:51,376 services 32577 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:35:51,536 services 32577 8539021056 Updated media request: 165
INFO 2025-07-29 12:35:51,663 services 32577 8539021056 Checking Plex availability for rating key 72947
INFO 2025-07-29 12:35:53,091 services 32577 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:35:53,330 services 32577 8539021056 Updated media request: 164
INFO 2025-07-29 12:35:53,403 services 32577 8539021056 Checking Plex availability for rating key 65139
INFO 2025-07-29 12:35:53,716 services 32577 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:35:53,759 services 32577 8539021056 Updated media request: 163
INFO 2025-07-29 12:35:53,828 services 32577 8539021056 Checking Plex availability for rating key 130288
INFO 2025-07-29 12:35:54,038 services 32577 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:35:54,054 services 32577 8539021056 Updated media request: 162
INFO 2025-07-29 12:35:54,152 services 32577 8539021056 Checking Plex availability for rating key 77791
INFO 2025-07-29 12:35:54,683 services 32577 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:35:54,741 services 32577 8539021056 Updated media request: 161
INFO 2025-07-29 12:35:54,830 services 32577 8539021056 Checking Plex availability for rating key 67674
INFO 2025-07-29 12:35:55,055 services 32577 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:35:55,082 services 32577 8539021056 Updated media request: 160
INFO 2025-07-29 12:35:55,166 services 32577 8539021056 Checking Plex availability for rating key 66668
INFO 2025-07-29 12:35:55,506 services 32577 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:35:55,535 services 32577 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:39:36,940 services 35370 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:39:36,941 services 35370 8539021056 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:39:37,001 services 35370 8539021056 Updated media request: 166
INFO 2025-07-29 12:39:37,125 services 35370 8539021056 Checking Plex availability and quality for rating key 63790
INFO 2025-07-29 12:39:53,115 services 35370 8539021056 Updated TV show: Taskmaster
INFO 2025-07-29 12:39:53,384 services 35370 8539021056 Updated media request: 165
INFO 2025-07-29 12:39:53,571 services 35370 8539021056 Checking Plex availability and quality for rating key 72947
INFO 2025-07-29 12:40:15,507 services 35370 8539021056 Updated TV show: Modern Family
INFO 2025-07-29 12:40:15,868 services 35370 8539021056 Updated media request: 164
INFO 2025-07-29 12:40:15,956 services 35370 8539021056 Checking Plex availability and quality for rating key 65139
INFO 2025-07-29 12:40:17,970 services 35370 8539021056 Updated TV show: The Night Agent
INFO 2025-07-29 12:40:18,015 services 35370 8539021056 Updated media request: 163
INFO 2025-07-29 12:40:18,094 services 35370 8539021056 Checking Plex availability and quality for rating key 130288
INFO 2025-07-29 12:40:18,807 services 35370 8539021056 Updated TV show: Red Eye
INFO 2025-07-29 12:40:18,831 services 35370 8539021056 Updated media request: 162
INFO 2025-07-29 12:40:18,933 services 35370 8539021056 Checking Plex availability and quality for rating key 77791
INFO 2025-07-29 12:40:21,887 services 35370 8539021056 Updated TV show: Yellowjackets
INFO 2025-07-29 12:40:21,957 services 35370 8539021056 Updated media request: 161
INFO 2025-07-29 12:40:22,036 services 35370 8539021056 Checking Plex availability and quality for rating key 67674
INFO 2025-07-29 12:40:23,621 services 35370 8539021056 Updated TV show: Based on a True Story
INFO 2025-07-29 12:40:23,651 services 35370 8539021056 Updated media request: 160
INFO 2025-07-29 12:40:23,739 services 35370 8539021056 Checking Plex availability and quality for rating key 66668
INFO 2025-07-29 12:40:25,710 services 35370 8539021056 Updated TV show: The White Lotus
INFO 2025-07-29 12:40:25,751 services 35370 8539021056 Successfully synced 7 TV requests
INFO 2025-07-29 12:49:53,806 scheduler 42552 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:49:53,807 apps 42552 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:50:02,562 scheduler 42656 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:50:02,562 apps 42656 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:50:16,423 scheduler 42833 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:50:16,423 apps 42833 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:50:36,349 scheduler 43068 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:50:36,349 apps 43068 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:50:55,325 scheduler 43302 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:50:55,326 apps 43302 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:51:40,647 scheduler 43816 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:51:40,647 apps 43816 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:51:58,660 scheduler 44118 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:51:58,660 apps 44118 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:52:10,486 scheduler 44244 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:52:10,486 apps 44244 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:07,883 scheduler 44940 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:07,883 apps 44940 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:08,063 scheduler 44943 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:08,063 apps 44943 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:21,380 services 45097 8539021056 Using python-overseerr library for API calls
ERROR 2025-07-29 12:53:21,406 services 45097 8539021056 Unexpected error in async search: 404, message='Not Found', url='http://*************:5055/api/v1/search/tv?query=Breaking+Bad&page=1'
WARNING 2025-07-29 12:53:21,407 services 45097 8539021056 Async search failed, falling back to requests: Unexpected error: 404, message='Not Found', url='http://*************:5055/api/v1/search/tv?query=Breaking+Bad&page=1'
INFO 2025-07-29 12:53:40,136 scheduler 45319 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:40,136 apps 45319 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:40,215 scheduler 45320 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:40,215 apps 45320 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:54,042 scheduler 45494 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:54,042 scheduler 45493 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:53:54,042 apps 45493 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:53:54,042 apps 45494 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:54:25,761 scheduler 45881 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:54:25,761 scheduler 45880 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:54:25,761 apps 45881 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:54:25,761 apps 45880 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:54:42,633 services 46078 8539021056 Using python-overseerr library for API calls
ERROR 2025-07-29 12:54:42,662 services 46078 8539021056 Unexpected error in async search: 400, message='Bad Request', url='http://*************:5055/api/v1/search?query=Breaking+Bad&page=1'
WARNING 2025-07-29 12:54:42,662 services 46078 8539021056 Async search failed, falling back to requests: Unexpected error: 400, message='Bad Request', url='http://*************:5055/api/v1/search?query=Breaking+Bad&page=1'
ERROR 2025-07-29 12:54:42,735 services 46078 8539021056 Overseerr API request failed: 400 Client Error: Bad Request for url: http://*************:5055/api/v1/search?query=Breaking+Bad&page=1
INFO 2025-07-29 12:55:45,380 scheduler 46802 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:55:45,380 scheduler 46801 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:55:45,380 apps 46802 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:55:45,380 apps 46801 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:55:57,244 scheduler 46970 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:55:57,244 apps 46970 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:55:57,244 scheduler 46969 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:55:57,244 apps 46969 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:56:22,187 services 47252 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:56:35,906 services 47426 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 12:57:29,635 scheduler 48040 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:58:44,939 scheduler 48909 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:58:44,939 scheduler 48910 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:58:44,940 apps 48909 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:58:44,940 apps 48910 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:58:52,868 services 48910 6206304256 Using python-overseerr library for API calls
INFO 2025-07-29 12:58:53,382 services 48910 6206304256 Using python-overseerr library for API calls
INFO 2025-07-29 12:58:56,311 services 48910 6206304256 Using python-overseerr library for API calls
INFO 2025-07-29 12:58:56,424 services 48910 6206304256 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:58:56,491 services 48910 6206304256 Updated media request: 166
INFO 2025-07-29 12:58:56,622 services 48910 6206304256 Checking Plex availability and quality for rating key 63790
INFO 2025-07-29 12:59:04,876 services 48910 6224277504 Using python-overseerr library for API calls
INFO 2025-07-29 12:59:04,876 services 48910 6224277504 Starting sync of TV show requests from Overseerr
INFO 2025-07-29 12:59:04,995 services 48910 6224277504 Updated media request: 166
INFO 2025-07-29 12:59:05,138 services 48910 6224277504 Checking Plex availability and quality for rating key 63790
INFO 2025-07-29 12:59:13,047 services 48910 6206304256 Updated TV show: Taskmaster
INFO 2025-07-29 12:59:13,248 services 48910 6206304256 Updated media request: 165
INFO 2025-07-29 12:59:13,397 services 48910 6206304256 Checking Plex availability and quality for rating key 72947
INFO 2025-07-29 12:59:21,977 services 48910 6224277504 Updated TV show: Taskmaster
INFO 2025-07-29 12:59:22,185 services 48910 6224277504 Updated media request: 165
INFO 2025-07-29 12:59:22,320 services 48910 6224277504 Checking Plex availability and quality for rating key 72947
INFO 2025-07-29 12:59:37,782 services 48910 6206304256 Updated TV show: Modern Family
INFO 2025-07-29 12:59:38,188 services 48910 6206304256 Updated media request: 164
INFO 2025-07-29 12:59:38,275 services 48910 6206304256 Checking Plex availability and quality for rating key 65139
INFO 2025-07-29 12:59:39,956 services 48910 6206304256 Updated TV show: The Night Agent
INFO 2025-07-29 12:59:40,000 services 48910 6206304256 Updated media request: 163
INFO 2025-07-29 12:59:40,057 services 48910 6206304256 Checking Plex availability and quality for rating key 130288
INFO 2025-07-29 12:59:40,601 services 48910 6206304256 Updated TV show: Red Eye
INFO 2025-07-29 12:59:40,619 services 48910 6206304256 Updated media request: 162
INFO 2025-07-29 12:59:40,698 services 48910 6206304256 Checking Plex availability and quality for rating key 77791
INFO 2025-07-29 12:59:43,459 services 48910 6206304256 Updated TV show: Yellowjackets
INFO 2025-07-29 12:59:43,517 services 48910 6206304256 Updated media request: 161
INFO 2025-07-29 12:59:43,597 services 48910 6206304256 Checking Plex availability and quality for rating key 67674
INFO 2025-07-29 12:59:44,900 services 48910 6206304256 Updated TV show: Based on a True Story
INFO 2025-07-29 12:59:44,931 services 48910 6206304256 Updated media request: 160
INFO 2025-07-29 12:59:45,018 services 48910 6206304256 Checking Plex availability and quality for rating key 66668
INFO 2025-07-29 12:59:45,610 services 48910 6224277504 Updated TV show: Modern Family
INFO 2025-07-29 12:59:45,763 scheduler 49644 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 12:59:45,763 apps 49644 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 12:59:45,971 services 48910 6224277504 Updated media request: 164
INFO 2025-07-29 12:59:46,048 services 48910 6224277504 Checking Plex availability and quality for rating key 65139
INFO 2025-07-29 12:59:46,738 services 48910 6206304256 Updated TV show: The White Lotus
INFO 2025-07-29 12:59:46,791 services 48910 6206304256 Successfully synced 7 TV requests
INFO 2025-07-29 12:59:47,797 services 48910 6224277504 Updated TV show: The Night Agent
INFO 2025-07-29 12:59:47,826 services 48910 6224277504 Updated media request: 163
INFO 2025-07-29 12:59:47,907 services 48910 6224277504 Checking Plex availability and quality for rating key 130288
INFO 2025-07-29 12:59:48,527 services 48910 6224277504 Updated TV show: Red Eye
INFO 2025-07-29 12:59:48,546 services 48910 6224277504 Updated media request: 162
INFO 2025-07-29 12:59:48,629 services 48910 6224277504 Checking Plex availability and quality for rating key 77791
INFO 2025-07-29 12:59:51,158 services 48910 6224277504 Updated TV show: Yellowjackets
INFO 2025-07-29 12:59:51,225 services 48910 6224277504 Updated media request: 161
INFO 2025-07-29 12:59:51,305 services 48910 6224277504 Checking Plex availability and quality for rating key 67674
INFO 2025-07-29 12:59:52,597 services 48910 6224277504 Updated TV show: Based on a True Story
INFO 2025-07-29 12:59:52,625 services 48910 6224277504 Updated media request: 160
INFO 2025-07-29 12:59:52,713 services 48910 6224277504 Checking Plex availability and quality for rating key 66668
INFO 2025-07-29 12:59:54,482 services 48910 6224277504 Updated TV show: The White Lotus
INFO 2025-07-29 12:59:54,512 services 48910 6224277504 Successfully synced 7 TV requests
INFO 2025-07-29 12:59:54,512 views 48910 6224277504 Manual data sync completed successfully
INFO 2025-07-29 13:00:00,592 services 48910 6224277504 Using python-overseerr library for API calls
INFO 2025-07-29 13:00:15,857 services 48910 6206304256 Using python-overseerr library for API calls
INFO 2025-07-29 13:01:40,844 scheduler 51005 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:01:40,844 scheduler 51006 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:01:40,844 apps 51005 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:01:40,844 apps 51006 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:03:41,077 scheduler 52429 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:03:41,077 apps 52429 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:03:41,077 scheduler 52430 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:03:41,077 apps 52430 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:04:02,104 scheduler 52682 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:04:02,104 apps 52682 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:04:02,104 scheduler 52681 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:04:02,104 apps 52681 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:04:57,543 scheduler 53334 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:04:57,542 scheduler 53333 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:04:57,543 apps 53333 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:04:57,543 apps 53334 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:05:08,343 scheduler 53458 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:05:08,343 scheduler 53459 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:05:08,343 apps 53458 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:05:08,343 apps 53459 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:05:26,345 scheduler 53654 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:05:26,345 scheduler 53655 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:05:26,345 apps 53654 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:05:26,345 apps 53655 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:06:04,503 scheduler 54135 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:06:04,503 apps 54135 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:06:04,504 scheduler 54134 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:06:04,504 apps 54134 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:07:11,232 services 54978 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 13:07:11,425 services 54978 8539021056 Checking Plex availability and quality for rating key 156727
INFO 2025-07-29 13:07:17,346 services 54135 6255456256 Using python-overseerr library for API calls
INFO 2025-07-29 13:07:19,825 services 54135 6255456256 Using python-overseerr library for API calls
INFO 2025-07-29 13:07:19,919 services 54135 6255456256 Checking Plex availability and quality for rating key 158498
INFO 2025-07-29 13:07:20,636 services 54135 6255456256 Created new TV show: Stick
INFO 2025-07-29 13:07:20,638 services 54135 6255456256 Created season 1 for Stick
INFO 2025-07-29 13:07:28,078 services 54978 8539021056 Created new TV show: The Office
INFO 2025-07-29 13:07:28,085 services 54978 8539021056 Created season 0 for The Office
INFO 2025-07-29 13:07:28,215 services 54978 8539021056 Created season 1 for The Office
INFO 2025-07-29 13:07:28,222 services 54978 8539021056 Created season 2 for The Office
INFO 2025-07-29 13:07:28,245 services 54978 8539021056 Created season 3 for The Office
INFO 2025-07-29 13:07:28,269 services 54978 8539021056 Created season 4 for The Office
INFO 2025-07-29 13:07:28,285 services 54978 8539021056 Created season 5 for The Office
INFO 2025-07-29 13:07:28,311 services 54978 8539021056 Created season 6 for The Office
INFO 2025-07-29 13:07:28,338 services 54978 8539021056 Created season 7 for The Office
INFO 2025-07-29 13:07:28,371 services 54978 8539021056 Created season 8 for The Office
INFO 2025-07-29 13:07:28,401 services 54978 8539021056 Created season 9 for The Office
INFO 2025-07-29 13:07:52,737 services 55505 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 13:07:52,737 services 55505 8539021056 Starting Plex data sync for all TV shows
INFO 2025-07-29 13:07:52,811 services 55505 8539021056 Checking Plex availability and quality for rating key 67674
INFO 2025-07-29 13:07:54,238 services 55505 8539021056 Checking Plex availability and quality for rating key 72947
INFO 2025-07-29 13:08:14,921 services 55505 8539021056 Checking Plex availability and quality for rating key 130288
INFO 2025-07-29 13:08:15,546 services 55505 8539021056 Checking Plex availability and quality for rating key 158498
INFO 2025-07-29 13:08:16,408 services 55505 8539021056 Checking Plex availability and quality for rating key 63790
INFO 2025-07-29 13:08:31,497 services 55505 8539021056 Checking Plex availability and quality for rating key 65139
INFO 2025-07-29 13:08:33,312 services 55505 8539021056 Checking Plex availability and quality for rating key 156727
INFO 2025-07-29 13:08:49,956 services 55505 8539021056 Checking Plex availability and quality for rating key 66668
INFO 2025-07-29 13:08:51,786 services 55505 8539021056 Checking Plex availability and quality for rating key 77791
INFO 2025-07-29 13:08:54,521 services 55505 8539021056 Successfully synced Plex data for 9 TV shows
INFO 2025-07-29 13:30:04,561 scheduler 82524 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:04,561 apps 82524 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:04,562 scheduler 82523 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:04,562 apps 82523 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:24,763 scheduler 82790 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:24,763 scheduler 82791 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:24,763 apps 82790 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:24,763 apps 82791 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:33,531 scheduler 82895 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:33,532 apps 82895 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:33,535 scheduler 82896 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:33,536 apps 82896 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:46,541 scheduler 83056 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:46,541 apps 83056 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:46,541 scheduler 83055 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:46,544 apps 83055 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:56,504 scheduler 83194 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:56,504 apps 83194 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:30:56,504 scheduler 83195 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:30:56,504 apps 83195 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:31:38,912 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:31:38,912 services 83194 6181040128 Deleting TV show: Breaking Bad (ID: 1) by Web User
INFO 2025-07-29 13:31:38,916 services 83194 6181040128 Successfully deleted TV show: Breaking Bad
INFO 2025-07-29 13:31:53,444 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:31:53,444 services 83194 6181040128 Deleting TV show: The Office (ID: 11) by Web User
INFO 2025-07-29 13:31:53,448 services 83194 6181040128 Successfully deleted TV show: The Office
INFO 2025-07-29 13:31:56,024 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:31:56,026 services 83194 6181040128 Deleting TV show: The Office (ID: 2) by Web User
INFO 2025-07-29 13:31:56,029 services 83194 6181040128 Successfully deleted TV show: The Office
INFO 2025-07-29 13:32:29,027 services 84272 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 13:32:29,211 services 84272 8539021056 Checking Plex availability and quality for rating key 70745
INFO 2025-07-29 13:32:49,643 services 84272 8539021056 Created new TV show: Friends
INFO 2025-07-29 13:32:49,646 services 84272 8539021056 Created season 0 for Friends
INFO 2025-07-29 13:32:49,692 services 84272 8539021056 Created season 1 for Friends
INFO 2025-07-29 13:32:49,720 services 84272 8539021056 Created season 2 for Friends
INFO 2025-07-29 13:32:49,747 services 84272 8539021056 Created season 3 for Friends
INFO 2025-07-29 13:32:49,775 services 84272 8539021056 Created season 4 for Friends
INFO 2025-07-29 13:32:49,802 services 84272 8539021056 Created season 5 for Friends
INFO 2025-07-29 13:32:49,827 services 84272 8539021056 Created season 6 for Friends
INFO 2025-07-29 13:32:49,853 services 84272 8539021056 Created season 7 for Friends
INFO 2025-07-29 13:32:49,877 services 84272 8539021056 Created season 8 for Friends
INFO 2025-07-29 13:32:49,902 services 84272 8539021056 Created season 9 for Friends
INFO 2025-07-29 13:32:49,929 services 84272 8539021056 Created season 10 for Friends
INFO 2025-07-29 13:32:49,948 services 84272 8539021056 Deleting TV show: Friends (ID: 12) by Test User
INFO 2025-07-29 13:32:49,951 services 84272 8539021056 Successfully deleted TV show: Friends
INFO 2025-07-29 13:37:15,373 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:37:15,374 services 83194 6181040128 Deleting TV show: The Night Agent (ID: 7) by Web User
INFO 2025-07-29 13:37:15,379 services 83194 6181040128 Successfully deleted TV show: The Night Agent
INFO 2025-07-29 13:37:37,082 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:37:39,280 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:37:42,160 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:37:42,259 services 83194 6181040128 Checking Plex availability and quality for rating key 152082
INFO 2025-07-29 13:37:43,078 services 83194 6181040128 Created new TV show: Murderbot
INFO 2025-07-29 13:37:43,079 services 83194 6181040128 Created season 1 for Murderbot
INFO 2025-07-29 13:37:53,026 services 88187 8539021056 Using python-overseerr library for API calls
INFO 2025-07-29 13:37:53,165 services 88187 8539021056 Checking Plex availability and quality for rating key 12200
INFO 2025-07-29 13:37:59,757 services 88187 8539021056 Created new TV show: Game of Thrones
INFO 2025-07-29 13:37:59,761 services 88187 8539021056 Created season 0 for Game of Thrones
INFO 2025-07-29 13:38:00,017 services 88187 8539021056 Created season 1 for Game of Thrones
INFO 2025-07-29 13:38:00,031 services 88187 8539021056 Created season 2 for Game of Thrones
INFO 2025-07-29 13:38:00,045 services 88187 8539021056 Created season 3 for Game of Thrones
INFO 2025-07-29 13:38:00,056 services 88187 8539021056 Created season 4 for Game of Thrones
INFO 2025-07-29 13:38:00,068 services 88187 8539021056 Created season 5 for Game of Thrones
INFO 2025-07-29 13:38:00,079 services 88187 8539021056 Created season 6 for Game of Thrones
INFO 2025-07-29 13:38:00,089 services 88187 8539021056 Created season 7 for Game of Thrones
INFO 2025-07-29 13:38:00,097 services 88187 8539021056 Created season 8 for Game of Thrones
INFO 2025-07-29 13:39:34,772 services 83194 6198439936 Using python-overseerr library for API calls
INFO 2025-07-29 13:40:08,702 services 83194 6181040128 Using python-overseerr library for API calls
INFO 2025-07-29 13:40:08,702 services 83194 6181040128 Deleting TV show: Game of Thrones (ID: 14) by Web User
INFO 2025-07-29 13:40:08,706 services 83194 6181040128 Successfully deleted TV show: Game of Thrones
INFO 2025-07-29 13:46:45,688 scheduler 94438 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:46:45,689 apps 94438 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:46:45,690 scheduler 94439 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:46:45,690 apps 94439 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:49:05,449 scheduler 96116 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:49:05,449 scheduler 96117 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:49:05,449 apps 96117 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:49:05,449 apps 96116 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:51:26,151 scheduler 97762 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:51:26,152 apps 97762 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:53:59,226 scheduler 99756 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:53:59,226 apps 99756 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:55:06,633 scheduler 812 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:55:06,633 apps 812 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:58:58,016 scheduler 3599 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:58:58,017 apps 3599 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:59:03,339 jackett_service 3599 6325039104 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 13:59:21,301 scheduler 3871 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 13:59:21,301 apps 3871 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 13:59:24,725 jackett_service 3871 6180777984 Searching Jackett: Murderbot S01E01 on all
ERROR 2025-07-29 13:59:24,842 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:24,842 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:24,842 jackett_service 3871 6180777984 Search failed for query 'Murderbot S01E01': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:24,842 jackett_service 3871 6180777984 Searching Jackett: Murderbot 1x01 on all
ERROR 2025-07-29 13:59:24,900 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:24,900 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:24,900 jackett_service 3871 6180777984 Search failed for query 'Murderbot 1x01': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:24,900 jackett_service 3871 6180777984 Searching Jackett: Murderbot Season 1 Episode 1 on all
ERROR 2025-07-29 13:59:24,954 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:24,954 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:24,954 jackett_service 3871 6180777984 Search failed for query 'Murderbot Season 1 Episode 1': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:30,220 jackett_service 3871 6180777984 Searching Jackett: Murderbot S01E01 on all
ERROR 2025-07-29 13:59:30,310 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:30,311 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:30,311 jackett_service 3871 6180777984 Search failed for query 'Murderbot S01E01': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:30,311 jackett_service 3871 6180777984 Searching Jackett: Murderbot 1x01 on all
ERROR 2025-07-29 13:59:30,364 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:30,365 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:30,365 jackett_service 3871 6180777984 Search failed for query 'Murderbot 1x01': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:30,365 jackett_service 3871 6180777984 Searching Jackett: Murderbot Season 1 Episode 1 on all
ERROR 2025-07-29 13:59:30,411 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:30,411 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:30,411 jackett_service 3871 6180777984 Search failed for query 'Murderbot Season 1 Episode 1': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:36,639 jackett_service 3871 6180777984 Searching Jackett: Murderbot S01E02 on all
ERROR 2025-07-29 13:59:36,721 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:36,721 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:36,721 jackett_service 3871 6180777984 Search failed for query 'Murderbot S01E02': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:36,721 jackett_service 3871 6180777984 Searching Jackett: Murderbot 1x02 on all
ERROR 2025-07-29 13:59:36,774 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:36,774 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:36,774 jackett_service 3871 6180777984 Search failed for query 'Murderbot 1x02': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 13:59:36,774 jackett_service 3871 6180777984 Searching Jackett: Murderbot Season 1 Episode 2 on all
ERROR 2025-07-29 13:59:36,814 jackett_service 3871 6180777984 Failed to parse XML response: not well-formed (invalid token): line 1, column 0
ERROR 2025-07-29 13:59:36,814 jackett_service 3871 6180777984 Unexpected error in Jackett search: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
WARNING 2025-07-29 13:59:36,814 jackett_service 3871 6180777984 Search failed for query 'Murderbot Season 1 Episode 2': Unexpected error: Failed to parse XML response: not well-formed (invalid token): line 1, column 0
INFO 2025-07-29 14:00:11,757 scheduler 4464 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:00:11,757 apps 4464 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:00:27,722 scheduler 4638 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:00:27,723 apps 4638 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:02:05,857 scheduler 5805 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:02:05,858 apps 5805 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:02:12,253 jackett_service 5805 6151368704 Searching Jackett: Murderbot S01E01 on all
ERROR 2025-07-29 14:02:12,366 jackett_service 5805 6151368704 Jackett API error: Unknown error from Jackett
ERROR 2025-07-29 14:02:12,366 jackett_service 5805 6151368704 Unexpected error in Jackett search: Jackett API error: Unknown error from Jackett
WARNING 2025-07-29 14:02:12,366 jackett_service 5805 6151368704 Search failed for query 'Murderbot S01E01': Unexpected error: Jackett API error: Unknown error from Jackett
INFO 2025-07-29 14:02:12,367 jackett_service 5805 6151368704 Searching Jackett: Murderbot 1x01 on all
ERROR 2025-07-29 14:02:12,417 jackett_service 5805 6151368704 Jackett API error: Unknown error from Jackett
ERROR 2025-07-29 14:02:12,417 jackett_service 5805 6151368704 Unexpected error in Jackett search: Jackett API error: Unknown error from Jackett
WARNING 2025-07-29 14:02:12,417 jackett_service 5805 6151368704 Search failed for query 'Murderbot 1x01': Unexpected error: Jackett API error: Unknown error from Jackett
INFO 2025-07-29 14:02:12,418 jackett_service 5805 6151368704 Searching Jackett: Murderbot Season 1 Episode 1 on all
ERROR 2025-07-29 14:02:12,478 jackett_service 5805 6151368704 Jackett API error: Unknown error from Jackett
ERROR 2025-07-29 14:02:12,479 jackett_service 5805 6151368704 Unexpected error in Jackett search: Jackett API error: Unknown error from Jackett
WARNING 2025-07-29 14:02:12,479 jackett_service 5805 6151368704 Search failed for query 'Murderbot Season 1 Episode 1': Unexpected error: Jackett API error: Unknown error from Jackett
INFO 2025-07-29 14:02:18,815 jackett_service 5805 6168768512 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers
ERROR 2025-07-29 14:02:18,914 jackett_service 5805 6168768512 Invalid JSON response from Jackett: Expecting value: line 1 column 1 (char 0)
INFO 2025-07-29 14:02:36,730 jackett_service 5805 6168768512 Searching Jackett: test on all
ERROR 2025-07-29 14:02:36,818 jackett_service 5805 6168768512 Jackett API error: Unknown error from Jackett
ERROR 2025-07-29 14:02:36,818 jackett_service 5805 6168768512 Unexpected error in Jackett search: Jackett API error: Unknown error from Jackett
ERROR 2025-07-29 14:02:36,818 views 5805 6168768512 Jackett test search failed: Unexpected error: Jackett API error: Unknown error from Jackett
INFO 2025-07-29 14:02:59,381 scheduler 6513 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:02:59,381 apps 6513 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:03:11,222 scheduler 6637 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:03:11,222 apps 6637 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:03:23,241 jackett_service 6637 6186758144 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers
ERROR 2025-07-29 14:03:23,319 jackett_service 6637 6186758144 Invalid JSON response from Jackett: Expecting value: line 1 column 1 (char 0)
ERROR 2025-07-29 14:03:23,319 jackett_service 6637 6186758144 Response content: <!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1.0" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta charset="utf-8" />
    <meta name="referrer" content="no-referrer" /> <!-- Don't send referrer when loading third party resources (E.g. E
ERROR 2025-07-29 14:03:23,319 jackett_service 6637 6186758144 Response status: 200
ERROR 2025-07-29 14:03:23,319 jackett_service 6637 6186758144 Response headers: {'Content-Type': 'text/html', 'Date': 'Tue, 29 Jul 2025 14:03:22 GMT', 'Server': 'Kestrel', 'Cache-Control': 'no-store,no-cache', 'Content-Encoding': 'gzip', 'Last-Modified': 'Mon, 28 Jul 2025 22:49:04 GMT', 'Pragma': 'no-cache', 'Transfer-Encoding': 'chunked', 'Vary': 'Accept-Encoding'}
INFO 2025-07-29 14:05:15,746 scheduler 8106 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:05:15,747 apps 8106 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:05:27,621 scheduler 8254 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:05:27,621 apps 8254 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:05:42,522 scheduler 8426 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:05:42,522 apps 8426 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:06:12,991 jackett_service 8426 6149697536 Searching Jackett: test on all
INFO 2025-07-29 14:06:42,448 jackett_service 8426 6149697536 Found 877 results for query: test
INFO 2025-07-29 14:07:28,469 jackett_service 8426 6149697536 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers/all/results/torznab/
INFO 2025-07-29 14:07:28,504 jackett_service 8426 6149697536 Successfully connected to Jackett API (XML response received)
ERROR 2025-07-29 14:07:28,577 jackett_service 8426 6149697536 Failed to get indexers: Expecting value: line 1 column 1 (char 0)
INFO 2025-07-29 14:07:38,250 jackett_service 8426 6149697536 Searching Jackett: test on all
INFO 2025-07-29 14:07:38,649 jackett_service 8426 6149697536 Found 877 results for query: test
INFO 2025-07-29 14:07:59,048 scheduler 10251 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:07:59,048 apps 10251 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:08:11,846 scheduler 10399 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:08:11,846 apps 10399 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:08:24,188 jackett_service 10399 6150516736 Searching Jackett: test on all
INFO 2025-07-29 14:08:24,495 jackett_service 10399 6150516736 Found 877 results for query: test
INFO 2025-07-29 14:08:42,311 jackett_service 10399 6150516736 Searching Jackett: test on all
INFO 2025-07-29 14:08:42,620 jackett_service 10399 6150516736 Found 877 results for query: test
INFO 2025-07-29 14:08:55,164 jackett_service 10399 6150516736 Searching Jackett: test on all
INFO 2025-07-29 14:08:55,486 jackett_service 10399 6150516736 Found 877 results for query: test
INFO 2025-07-29 14:09:08,238 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:09:15,304 jackett_service 10399 6150516736 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:09:15,305 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:09:23,911 jackett_service 10399 6150516736 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:09:23,912 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:09:32,650 jackett_service 10399 6150516736 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 14:09:50,312 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:09:50,387 jackett_service 10399 6150516736 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:09:50,387 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:09:50,402 jackett_service 10399 6150516736 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:09:50,402 jackett_service 10399 6150516736 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:09:50,416 jackett_service 10399 6150516736 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 14:10:11,176 scheduler 11854 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:10:11,176 apps 11854 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:10:16,773 jackett_service 11854 6225063936 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:10:23,841 jackett_service 11854 6225063936 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:10:23,841 jackett_service 11854 6225063936 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:10:32,365 jackett_service 11854 6225063936 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:10:32,366 jackett_service 11854 6225063936 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:10:41,048 jackett_service 11854 6225063936 Found 0 results for query: Murderbot Season 1 Episode 1
INFO 2025-07-29 14:11:08,917 views 11854 6225063936 Download requested for: Murderbot.S01E01.1080p.ATVP.WEB-DL.DDP5.1.Atmos.H.264-FLUX
INFO 2025-07-29 14:11:08,918 views 11854 6225063936 Download URL: http://*************:9117/dl/1337x/?jackett_apikey=d6xea6xcsdknmsbw6x4vcwjdvj7fo3q1&path=Q2ZESjhJZjJ1NVR2TDNWTnRGWHpzYTdhUG9leTFneVUtcU1MYXpsRDk4NC1NR2NxaXU0aURXbXJ0QlYzdUJ1cDgxTlYzd1c1MVhkT0VSdW9aMk1nWjV2RGdHY0VZcERqMzc2VVczMlFHS3gzTUEyejNwMHZqS1NDX29oaWFaRWlCaTdaU3dabndxdmZCWFBYWnMwemM2UVJHSWpnSkdFdmYwN2hGRlBkbzh5VlUyZ3AtdVBYNXdzYWZ6dTFPdlJCTjVDQUo2VXNZT1gyYlVLb0V2MmNHdG5pUWIzZjJBQkk0WDdIWnNFOWkzV3Rpckdm&file=Murderbot.S01E01.1080p.ATVP.WEB-DL.DDP5.1.Atmos.H.264-FLUX
INFO 2025-07-29 14:11:08,918 views 11854 6225063936 Magnet URL: 
INFO 2025-07-29 14:12:03,618 jackett_service 11854 6225063936 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers/all/results/torznab/
INFO 2025-07-29 14:12:03,650 jackett_service 11854 6225063936 Successfully connected to Jackett API (XML response received)
ERROR 2025-07-29 14:12:03,725 jackett_service 11854 6225063936 Failed to get indexers: Expecting value: line 1 column 1 (char 0)
INFO 2025-07-29 14:12:39,242 scheduler 13536 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:12:39,242 apps 13536 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:12:49,273 scheduler 13661 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:12:49,274 apps 13661 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:13:03,381 scheduler 13814 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:13:03,381 apps 13814 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:13:18,750 scheduler 14020 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:13:18,751 apps 14020 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:13:31,188 scheduler 14144 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:13:31,188 apps 14144 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:13:51,882 jackett_service 14144 6176649216 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:13:51,997 jackett_service 14144 6176649216 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:13:51,998 jackett_service 14144 6176649216 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:13:52,011 jackett_service 14144 6176649216 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:13:52,011 jackett_service 14144 6176649216 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:13:52,021 jackett_service 14144 6176649216 Found 0 results for query: Murderbot Season 1 Episode 1
ERROR 2025-07-29 14:13:52,022 views 14144 6176649216 Unexpected error in Jackett search: 'JackettService' object has no attribute '_get_quality_priority'
INFO 2025-07-29 14:15:05,490 jackett_service 14144 6176649216 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:15:05,600 jackett_service 14144 6176649216 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:15:05,600 jackett_service 14144 6176649216 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:15:05,615 jackett_service 14144 6176649216 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:15:05,615 jackett_service 14144 6176649216 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:15:05,628 jackett_service 14144 6176649216 Found 0 results for query: Murderbot Season 1 Episode 1
ERROR 2025-07-29 14:15:05,629 views 14144 6176649216 Unexpected error in Jackett search: 'JackettService' object has no attribute '_get_quality_priority'
INFO 2025-07-29 14:15:58,456 scheduler 15880 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:15:58,456 apps 15880 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:16:23,680 scheduler 16180 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:16:23,681 apps 16180 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:16:38,679 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:16:38,750 jackett_service 16180 6223310848 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:16:38,751 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:16:38,764 jackett_service 16180 6223310848 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:16:38,764 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:16:38,777 jackett_service 16180 6223310848 Found 0 results for query: Breaking Bad Season 1 Episode 1
ERROR 2025-07-29 14:16:38,777 views 16180 6223310848 Unexpected error in Jackett search: 'JackettService' object has no attribute '_get_quality_priority'
INFO 2025-07-29 14:16:47,758 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:16:47,817 jackett_service 16180 6223310848 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:16:47,817 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:16:47,833 jackett_service 16180 6223310848 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:16:47,834 jackett_service 16180 6223310848 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:16:47,846 jackett_service 16180 6223310848 Found 0 results for query: Breaking Bad Season 1 Episode 1
ERROR 2025-07-29 14:16:47,847 views 16180 6223310848 Unexpected error in Jackett search: 'JackettService' object has no attribute '_get_quality_priority'
INFO 2025-07-29 14:19:12,513 scheduler 18143 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:19:12,513 apps 18143 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:19:21,905 scheduler 18269 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:19:21,906 apps 18269 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:19:34,069 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:19:34,138 jackett_service 18269 6140637184 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:19:34,138 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:19:34,154 jackett_service 18269 6140637184 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:19:34,155 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:19:34,166 jackett_service 18269 6140637184 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 14:19:43,884 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad S01 on all
INFO 2025-07-29 14:19:51,211 jackett_service 18269 6140637184 Found 165 results for query: Breaking Bad S01
INFO 2025-07-29 14:19:51,212 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad Season 1 on all
INFO 2025-07-29 14:20:00,453 jackett_service 18269 6140637184 Found 122 results for query: Breaking Bad Season 1
INFO 2025-07-29 14:20:00,454 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad Complete Season 1 on all
INFO 2025-07-29 14:20:09,484 jackett_service 18269 6140637184 Found 57 results for query: Breaking Bad Complete Season 1
INFO 2025-07-29 14:20:09,486 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad S01 Complete on all
INFO 2025-07-29 14:20:18,360 jackett_service 18269 6140637184 Found 46 results for query: Breaking Bad S01 Complete
INFO 2025-07-29 14:21:06,944 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad S01 on all
INFO 2025-07-29 14:21:07,087 jackett_service 18269 6140637184 Found 165 results for query: Breaking Bad S01
INFO 2025-07-29 14:21:07,088 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad Season 1 on all
INFO 2025-07-29 14:21:07,152 jackett_service 18269 6140637184 Found 122 results for query: Breaking Bad Season 1
INFO 2025-07-29 14:21:07,152 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad Complete Season 1 on all
INFO 2025-07-29 14:21:07,187 jackett_service 18269 6140637184 Found 57 results for query: Breaking Bad Complete Season 1
INFO 2025-07-29 14:21:07,187 jackett_service 18269 6140637184 Searching Jackett: Breaking Bad S01 Complete on all
INFO 2025-07-29 14:21:07,220 jackett_service 18269 6140637184 Found 46 results for query: Breaking Bad S01 Complete
INFO 2025-07-29 14:21:18,717 jackett_service 18269 6140637184 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:21:18,888 jackett_service 18269 6140637184 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:21:18,888 jackett_service 18269 6140637184 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:21:18,918 jackett_service 18269 6140637184 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:21:18,918 jackett_service 18269 6140637184 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:21:18,942 jackett_service 18269 6140637184 Found 0 results for query: Murderbot Season 1 Episode 1
INFO 2025-07-29 14:21:36,216 jackett_service 18269 6158036992 Searching Jackett: Breaking Bad S01 on all
INFO 2025-07-29 14:21:36,343 jackett_service 18269 6158036992 Found 165 results for query: Breaking Bad S01
INFO 2025-07-29 14:21:36,343 jackett_service 18269 6158036992 Searching Jackett: Breaking Bad Season 1 on all
INFO 2025-07-29 14:21:36,409 jackett_service 18269 6158036992 Found 122 results for query: Breaking Bad Season 1
INFO 2025-07-29 14:21:36,410 jackett_service 18269 6158036992 Searching Jackett: Breaking Bad Complete Season 1 on all
INFO 2025-07-29 14:21:36,441 jackett_service 18269 6158036992 Found 57 results for query: Breaking Bad Complete Season 1
INFO 2025-07-29 14:21:36,441 jackett_service 18269 6158036992 Searching Jackett: Breaking Bad S01 Complete on all
INFO 2025-07-29 14:21:36,471 jackett_service 18269 6158036992 Found 46 results for query: Breaking Bad S01 Complete
INFO 2025-07-29 14:21:59,799 scheduler 20186 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:21:59,799 apps 20186 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:22:02,224 jackett_service 20186 6207516672 Searching Jackett: Murderbot S01 on all
INFO 2025-07-29 14:22:09,250 jackett_service 20186 6207516672 Found 252 results for query: Murderbot S01
INFO 2025-07-29 14:22:09,250 jackett_service 20186 6207516672 Searching Jackett: Murderbot Season 1 on all
INFO 2025-07-29 14:22:18,039 jackett_service 20186 6207516672 Found 10 results for query: Murderbot Season 1
INFO 2025-07-29 14:22:18,039 jackett_service 20186 6207516672 Searching Jackett: Murderbot Complete Season 1 on all
INFO 2025-07-29 14:22:26,924 jackett_service 20186 6207516672 Found 4 results for query: Murderbot Complete Season 1
INFO 2025-07-29 14:22:26,924 jackett_service 20186 6207516672 Searching Jackett: Murderbot S01 Complete on all
INFO 2025-07-29 14:22:35,847 jackett_service 20186 6207516672 Found 3 results for query: Murderbot S01 Complete
INFO 2025-07-29 14:24:42,108 scheduler 22139 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:24:42,109 apps 22139 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:24:53,754 scheduler 23080 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:24:53,754 apps 23080 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:25:07,209 scheduler 23382 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:25:07,209 apps 23382 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:25:18,431 scheduler 23510 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:25:18,431 apps 23510 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:25:35,552 scheduler 23724 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:25:35,553 apps 23724 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:25:52,691 scheduler 23929 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:25:52,691 apps 23929 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:26:01,626 scheduler 24039 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:26:01,626 apps 24039 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:26:12,950 scheduler 25146 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:26:12,950 apps 25146 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:26:24,118 scheduler 25276 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:26:24,119 apps 25276 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:26:35,169 scheduler 25410 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:26:35,169 apps 25410 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:26:46,311 scheduler 25543 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:26:46,311 apps 25543 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:27:07,308 jackett_service 25543 6178074624 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:27:07,427 jackett_service 25543 6178074624 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:27:07,427 jackett_service 25543 6178074624 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:27:07,444 jackett_service 25543 6178074624 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:27:07,444 jackett_service 25543 6178074624 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:27:07,457 jackett_service 25543 6178074624 Found 0 results for query: Murderbot Season 1 Episode 1
INFO 2025-07-29 14:41:22,971 jackett_service 25543 6178074624 Searching Jackett: Murderbot S01E01 on all
INFO 2025-07-29 14:41:23,817 jackett_service 25543 6178074624 Found 91 results for query: Murderbot S01E01
INFO 2025-07-29 14:41:23,817 jackett_service 25543 6178074624 Searching Jackett: Murderbot 1x01 on all
INFO 2025-07-29 14:41:23,847 jackett_service 25543 6178074624 Found 0 results for query: Murderbot 1x01
INFO 2025-07-29 14:41:23,847 jackett_service 25543 6178074624 Searching Jackett: Murderbot Season 1 Episode 1 on all
INFO 2025-07-29 14:41:23,881 jackett_service 25543 6178074624 Found 0 results for query: Murderbot Season 1 Episode 1
INFO 2025-07-29 14:42:40,874 scheduler 30520 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:42:40,875 apps 30520 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:43:17,671 scheduler 30939 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 14:43:17,672 apps 30939 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 14:43:41,600 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:43:41,839 jackett_service 30939 6211432448 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:43:41,840 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:43:41,875 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:43:41,875 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:43:41,908 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 14:43:55,850 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 14:43:55,991 jackett_service 30939 6211432448 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 14:43:55,992 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 14:43:56,021 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 14:43:56,021 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 14:43:56,095 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 14:44:08,315 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01 on all
INFO 2025-07-29 14:44:08,926 jackett_service 30939 6211432448 Found 165 results for query: Breaking Bad S01
INFO 2025-07-29 14:44:08,926 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Season 1 on all
INFO 2025-07-29 14:44:09,148 jackett_service 30939 6211432448 Found 122 results for query: Breaking Bad Season 1
INFO 2025-07-29 14:44:09,148 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Complete Season 1 on all
INFO 2025-07-29 14:44:09,268 jackett_service 30939 6211432448 Found 57 results for query: Breaking Bad Complete Season 1
INFO 2025-07-29 14:44:09,268 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01 Complete on all
INFO 2025-07-29 14:44:09,418 jackett_service 30939 6211432448 Found 46 results for query: Breaking Bad S01 Complete
INFO 2025-07-29 14:44:22,967 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01 on all
INFO 2025-07-29 14:44:23,847 jackett_service 30939 6211432448 Found 165 results for query: Breaking Bad S01
INFO 2025-07-29 14:44:23,848 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Season 1 on all
INFO 2025-07-29 14:44:24,144 jackett_service 30939 6211432448 Found 122 results for query: Breaking Bad Season 1
INFO 2025-07-29 14:44:24,144 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Complete Season 1 on all
INFO 2025-07-29 14:44:24,270 jackett_service 30939 6211432448 Found 57 results for query: Breaking Bad Complete Season 1
INFO 2025-07-29 14:44:24,270 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01 Complete on all
INFO 2025-07-29 14:44:24,390 jackett_service 30939 6211432448 Found 46 results for query: Breaking Bad S01 Complete
INFO 2025-07-29 15:11:18,166 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad S01E01 on all
INFO 2025-07-29 15:11:27,007 jackett_service 30939 6211432448 Found 20 results for query: Breaking Bad S01E01
INFO 2025-07-29 15:11:27,007 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad 1x01 on all
INFO 2025-07-29 15:11:35,642 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad 1x01
INFO 2025-07-29 15:11:35,643 jackett_service 30939 6211432448 Searching Jackett: Breaking Bad Season 1 Episode 1 on all
INFO 2025-07-29 15:11:44,268 jackett_service 30939 6211432448 Found 0 results for query: Breaking Bad Season 1 Episode 1
INFO 2025-07-29 15:12:35,562 scheduler 33815 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:12:35,563 apps 33815 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:14:02,542 scheduler 34838 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:14:02,542 apps 34838 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:14:53,775 scheduler 35420 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:14:53,775 apps 35420 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:14:57,142 jackett_service 35420 6139129856 Searching Jackett: Murderbot S01 on all
INFO 2025-07-29 15:15:04,845 jackett_service 35420 6139129856 Found 252 results for query: Murderbot S01
INFO 2025-07-29 15:15:04,845 jackett_service 35420 6139129856 Searching Jackett: Murderbot Season 1 on all
INFO 2025-07-29 15:15:13,510 jackett_service 35420 6139129856 Found 10 results for query: Murderbot Season 1
INFO 2025-07-29 15:15:13,511 jackett_service 35420 6139129856 Searching Jackett: Murderbot Complete Season 1 on all
INFO 2025-07-29 15:15:22,092 jackett_service 35420 6139129856 Found 4 results for query: Murderbot Complete Season 1
INFO 2025-07-29 15:15:22,092 jackett_service 35420 6139129856 Searching Jackett: Murderbot S01 Complete on all
INFO 2025-07-29 15:15:30,886 jackett_service 35420 6139129856 Found 3 results for query: Murderbot S01 Complete
INFO 2025-07-29 15:15:35,089 views 35420 6139129856 Download requested for: Murderbot S01 2160p ATVP WEB-DL DDP5 1 Atmos DV HDR H 265-FLUX
INFO 2025-07-29 15:15:35,089 views 35420 6139129856 Download URL: http://*************:9117/dl/torrentleech/?jackett_apikey=d6xea6xcsdknmsbw6x4vcwjdvj7fo3q1&path=Q2ZESjhJZjJ1NVR2TDNWTnRGWHpzYTdhUG9lTHBYT1VJb1p2MjYwRXFhOExOeDZKNE96YUkxMERxY2l2bVVlamNORUxhbmVadF9RY2V6NVRLRm90cFgxVFhTNkpSaEpXdDl6Z3R3WE9qR0FsSzNQRXFtWHAtZUFRYnplU1dMNFlpUndqeExqcWN3RkxVdE5JQ09BSTZmNVRQTmlZS0tGcmRIRkpwbGp3Rl9sZVlZWjFqejdYbGktWnd6ajM2M2RPMmo1YVNHTXExTm83Nk8wUTJ4emp2WnN5NVFHN3hHNnJacjRiUW5YN2xvQVUxX0lJekJGVmtTMDk0UVNZNlQwY3Z2MDJWcnJyTmY0aDA5WXQ5empLaVEzcXd5WQ&file=Murderbot+S01+2160p+ATVP+WEB-DL+DDP5+1+Atmos+DV+HDR+H+265-FLUX
INFO 2025-07-29 15:15:35,089 views 35420 6139129856 Magnet URL: 
INFO 2025-07-29 15:20:03,957 scheduler 39131 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:20:03,957 apps 39131 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:20:17,219 scheduler 39280 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:20:17,219 apps 39280 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:20:49,760 scheduler 39647 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:20:49,760 apps 39647 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-29 15:21:15,131 scheduler 39950 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-29 15:21:15,131 apps 39950 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:17:13,599 scheduler 75569 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:17:13,600 apps 75569 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:29:42,095 scheduler 84379 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:29:42,095 apps 84379 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:36:46,098 jackett_service 84379 12918534144 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers/all/results/torznab/
INFO 2025-07-30 08:36:48,856 jackett_service 84379 12901707776 Testing Jackett connection to: http://*************:9117/api/v2.0/indexers/all/results/torznab/
ERROR 2025-07-30 08:36:56,133 jackett_service 84379 12918534144 Jackett connection timeout: HTTPConnectionPool(host='*************', port=9117): Read timed out. (read timeout=10)
ERROR 2025-07-30 08:36:58,872 jackett_service 84379 12901707776 Jackett connection timeout: HTTPConnectionPool(host='*************', port=9117): Read timed out. (read timeout=10)
INFO 2025-07-30 08:37:11,234 scheduler 89693 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:37:11,234 apps 89693 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:37:37,431 scheduler 89993 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:37:37,431 apps 89993 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:37:55,629 scheduler 90228 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:37:55,629 apps 90228 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:43:41,423 scheduler 94253 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:43:41,423 apps 94253 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:47:42,044 scheduler 97127 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:47:42,044 apps 97127 8539021056 Overseerr scheduler started with Django application
INFO 2025-07-30 08:49:55,668 scheduler 98663 8539021056 Overseerr scheduler started - sync will run every hour
INFO 2025-07-30 08:49:55,669 apps 98663 8539021056 Overseerr scheduler started with Django application
