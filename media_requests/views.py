from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import TVShow, Season, Episode, MediaRequest
from .services import OverseerrDataSyncService, OverseerrAPIError, TVShowService
from .scheduler import get_scheduler_status
from .jackett_service import JackettService, JackettAPIError
import logging

logger = logging.getLogger(__name__)


def dashboard(request):
    """Main dashboard view showing all TV shows"""
    # Get filter and sort parameters
    availability_filter = request.GET.get('availability', '')
    sort_by = request.GET.get('sort', 'title')
    search_query = request.GET.get('search', '')

    # Get view mode from URL parameter, cookie, or default to grid
    view_mode = request.GET.get('view')
    if not view_mode:
        view_mode = request.COOKIES.get('dashboard_view_mode', 'grid')

    # Validate view mode
    if view_mode not in ['grid', 'table']:
        view_mode = 'grid'

    # Base queryset
    tv_shows = TVShow.objects.select_related('media_request').prefetch_related('seasons__episodes')

    # Apply search filter
    if search_query:
        tv_shows = tv_shows.filter(
            Q(title__icontains=search_query) |
            Q(overview__icontains=search_query)
        )

    # Apply availability filter
    if availability_filter:
        tv_shows = tv_shows.filter(availability_status=availability_filter)

    # Apply sorting
    sort_options = {
        'title': 'title',
        '-title': '-title',
        'date_added': 'created_at',
        '-date_added': '-created_at',
        'air_date': 'first_air_date',
        '-air_date': '-first_air_date',
        'request_date': 'media_request__requested_at',
        '-request_date': '-media_request__requested_at',
    }

    if sort_by in sort_options:
        tv_shows = tv_shows.order_by(sort_options[sort_by])

    # Pagination - adjust items per page based on view mode
    items_per_page = 12 if view_mode == 'grid' else 25
    paginator = Paginator(tv_shows, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    stats = {
        'total_shows': TVShow.objects.count(),
        'available_shows': TVShow.objects.filter(availability_status='available').count(),
        'partial_shows': TVShow.objects.filter(availability_status='partial').count(),
        'unavailable_shows': TVShow.objects.filter(availability_status='unavailable').count(),
    }

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'current_filters': {
            'availability': availability_filter,
            'sort': sort_by,
            'search': search_query,
            'view': view_mode,
        },
        'availability_choices': TVShow.AVAILABILITY_CHOICES,
        'sort_choices': [
            ('title', 'Title A-Z'),
            ('-title', 'Title Z-A'),
            ('date_added', 'Date Added (Oldest)'),
            ('-date_added', 'Date Added (Newest)'),
            ('air_date', 'Air Date (Oldest)'),
            ('-air_date', 'Air Date (Newest)'),
            ('request_date', 'Request Date (Oldest)'),
            ('-request_date', 'Request Date (Newest)'),
        ],
    }

    response = render(request, 'media_requests/dashboard.html', context)

    # Set view mode cookie if it was changed via URL parameter
    if request.GET.get('view') and request.GET.get('view') != request.COOKIES.get('dashboard_view_mode'):
        response.set_cookie('dashboard_view_mode', view_mode, max_age=365*24*60*60)  # 1 year

    return response


def show_detail(request, show_id):
    """Detailed view for a specific TV show"""
    tv_show = get_object_or_404(
        TVShow.objects.select_related('media_request').prefetch_related(
            'seasons__episodes__quality',
            'seasons__quality'
        ),
        id=show_id
    )

    # Get seasons with episode counts
    seasons = tv_show.seasons.annotate(
        total_episodes=Count('episodes'),
        available_episodes=Count('episodes', filter=Q(episodes__availability_status='available'))
    ).order_by('season_number')

    context = {
        'tv_show': tv_show,
        'seasons': seasons,
    }

    return render(request, 'media_requests/show_detail.html', context)


def season_episodes_ajax(request, season_id):
    """AJAX view to get episodes for a season"""
    season = get_object_or_404(Season, id=season_id)
    episodes = season.episodes.select_related('quality').order_by('episode_number')

    episodes_data = []
    for episode in episodes:
        episodes_data.append({
            'id': episode.id,
            'episode_number': episode.episode_number,
            'name': episode.name,
            'overview': episode.overview,
            'air_date': episode.air_date.isoformat() if episode.air_date else None,
            'runtime': episode.runtime,
            'availability_status': episode.availability_status,
            'quality': {
                'resolution': episode.quality.resolution if episode.quality else None,
                'source': episode.quality.source if episode.quality else None,
                'codec': episode.quality.codec if episode.quality else None,
            } if episode.quality else None,
        })

    return JsonResponse({
        'episodes': episodes_data,
        'season': {
            'id': season.id,
            'season_number': season.season_number,
            'name': season.name,
            'availability_status': season.availability_status,
        }
    })


def sync_data(request):
    """View to trigger data synchronization from Overseerr"""
    if request.method == 'POST':
        try:
            sync_service = OverseerrDataSyncService()
            sync_service.sync_all_tv_requests()

            messages.success(request, 'Successfully synchronized data from Overseerr!')
            logger.info('Manual data sync completed successfully')

        except OverseerrAPIError as e:
            messages.error(request, f'Failed to sync data from Overseerr: {e}')
            logger.error(f'Manual data sync failed: {e}')
        except Exception as e:
            messages.error(request, f'Unexpected error during sync: {e}')
            logger.error(f'Unexpected error during manual sync: {e}')

    return redirect('dashboard')


def api_status(request):
    """API endpoint to check Overseerr connection status"""
    try:
        sync_service = OverseerrDataSyncService()
        # Try to make a simple API call to test connection
        sync_service.client._make_request('/request', params={'take': 1})

        return JsonResponse({
            'status': 'connected',
            'message': 'Successfully connected to Overseerr API'
        })
    except OverseerrAPIError as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Unexpected error: {e}'
        }, status=500)


def scheduler_status(request):
    """API endpoint to check scheduler status"""
    try:
        status = get_scheduler_status()
        return JsonResponse(status)
    except Exception as e:
        return JsonResponse({
            'running': False,
            'error': str(e)
        }, status=500)


def search_shows(request):
    """Search for TV shows via Overseerr API"""
    query = request.GET.get('q', '').strip()
    page = int(request.GET.get('page', 1))

    if not query:
        return JsonResponse({
            'results': [],
            'total_results': 0,
            'total_pages': 0,
            'page': page
        })

    try:
        sync_service = OverseerrDataSyncService()
        search_results = sync_service.client.search_tv_shows(query, page)

        return JsonResponse(search_results)

    except OverseerrAPIError as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'error': f'Unexpected error: {e}'
        }, status=500)


def add_show(request):
    """Add a TV show directly to our database"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body)
        tmdb_id = data.get('tmdb_id')
        added_by = data.get('added_by', 'Web User')

        if not tmdb_id:
            return JsonResponse({'error': 'tmdb_id is required'}, status=400)

        # Check if show already exists
        if TVShow.objects.filter(tmdb_id=tmdb_id).exists():
            return JsonResponse({
                'error': 'Show already exists in database'
            }, status=400)

        # Add show to our database
        tv_service = TVShowService()
        tv_show = tv_service.add_tv_show_from_tmdb(tmdb_id, added_by)

        return JsonResponse({
            'success': True,
            'message': f'Successfully added "{tv_show.title}" to your library',
            'show_id': tv_show.id,
            'show_title': tv_show.title
        })

    except Exception as e:
        logger.error(f"Failed to add show: {e}")
        return JsonResponse({
            'error': f'Failed to add show: {str(e)}'
        }, status=500)


def delete_show(request, show_id):
    """Delete a TV show from our database"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body) if request.body else {}
        deleted_by = data.get('deleted_by', 'Web User')

        # Get the show to check if it exists and get its title
        try:
            tv_show = TVShow.objects.get(id=show_id)
            show_title = tv_show.title
        except TVShow.DoesNotExist:
            return JsonResponse({
                'error': 'Show not found'
            }, status=404)

        # Delete the show
        tv_service = TVShowService()
        success = tv_service.delete_tv_show(show_id, deleted_by)

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Successfully deleted "{show_title}"'
            })
        else:
            return JsonResponse({
                'error': 'Failed to delete show'
            }, status=500)

    except Exception as e:
        logger.error(f"Failed to delete show: {e}")
        return JsonResponse({
            'error': f'Failed to delete show: {str(e)}'
        }, status=500)


def jackett_search_episode(request):
    """Search for a specific episode using Jackett"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title', '').strip()
        season = request.GET.get('season', '')
        episode = request.GET.get('episode', '')
        indexer = request.GET.get('indexer', 'all')
        filter_results = request.GET.get('filter', 'true').lower() == 'true'

        # Validate parameters
        if not show_title:
            return JsonResponse({'error': 'show_title is required'}, status=400)

        if not season or not episode:
            return JsonResponse({'error': 'season and episode are required'}, status=400)

        try:
            season_num = int(season)
            episode_num = int(episode)
        except ValueError:
            return JsonResponse({'error': 'season and episode must be integers'}, status=400)

        # Initialize Jackett service
        jackett_service = JackettService()

        # Perform search
        results = jackett_service.search_episode(show_title, season_num, episode_num, indexer, filter_results)

        # Convert results to JSON-serializable format
        results_data = [result.to_dict() for result in results]

        return JsonResponse({
            'success': True,
            'results': results_data,
            'total_results': len(results_data),
            'search_query': f"{show_title} S{season_num:02d}E{episode_num:02d}",
            'filtered': filter_results,
        })

    except JackettAPIError as e:
        logger.error(f"Jackett search failed: {e}")
        return JsonResponse({
            'error': f'Jackett search failed: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in Jackett search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_status(request):
    """Check Jackett connection status"""
    try:
        jackett_service = JackettService()

        # Get configuration info
        config_info = {
            'base_url': jackett_service.base_url,
            'api_key_configured': bool(jackett_service.api_key),
            'api_key_length': len(jackett_service.api_key) if jackett_service.api_key else 0
        }

        # Test connection
        is_connected = jackett_service.test_connection()

        if is_connected:
            # Get indexers if connection is successful
            indexers = jackett_service.get_indexers()

            return JsonResponse({
                'status': 'connected',
                'message': 'Successfully connected to Jackett API',
                'config': config_info,
                'indexers': indexers,
                'total_indexers': len(indexers),
                'configured_indexers': len([idx for idx in indexers if idx.get('configured', False)])
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Failed to connect to Jackett API',
                'config': config_info,
                'help': 'Check your JACKETT_BASE_URL and JACKETT_API_KEY in .env file'
            }, status=500)

    except JackettAPIError as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e),
            'help': 'Check your Jackett configuration and ensure Jackett is running'
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Unexpected error: {e}',
            'help': 'Check your Jackett configuration and ensure Jackett is running'
        }, status=500)


def jackett_download(request):
    """Handle download request for a torrent (placeholder for now)"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        import json
        data = json.loads(request.body)

        # Get download parameters
        download_url = data.get('download_url', '')
        magnet_url = data.get('magnet_url', '')
        title = data.get('title', '')

        if not download_url and not magnet_url:
            return JsonResponse({'error': 'download_url or magnet_url is required'}, status=400)

        # For now, just show an alert (as requested)
        # In the future, this would integrate with a download client
        logger.info(f"Download requested for: {title}")
        logger.info(f"Download URL: {download_url}")
        logger.info(f"Magnet URL: {magnet_url}")

        return JsonResponse({
            'success': True,
            'message': f'Download request received for "{title}". This is a placeholder - actual download functionality will be implemented later.',
            'title': title,
            'download_url': download_url,
            'magnet_url': magnet_url
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Failed to process download request: {e}")
        return JsonResponse({
            'error': f'Failed to process download request: {str(e)}'
        }, status=500)


def jackett_test_search(request):
    """Test search endpoint for debugging"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get test parameters
        query = request.GET.get('q', 'test')

        # Initialize Jackett service
        jackett_service = JackettService()

        # Test basic search
        results = jackett_service.search(query, indexer='all', category='5000')

        return JsonResponse({
            'success': True,
            'query': query,
            'results_count': len(results),
            'results': [result.to_dict() for result in results[:5]],  # First 5 results
            'debug_info': {
                'base_url': jackett_service.base_url,
                'api_key_configured': bool(jackett_service.api_key)
            }
        })

    except JackettAPIError as e:
        logger.error(f"Jackett test search failed: {e}")
        return JsonResponse({
            'error': f'Jackett search failed: {str(e)}',
            'debug_info': {
                'base_url': getattr(jackett_service, 'base_url', 'Not initialized'),
                'api_key_configured': bool(getattr(jackett_service, 'api_key', False))
            }
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in test search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def jackett_search_season(request):
    """Search for a complete season using Jackett"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Get parameters
        show_title = request.GET.get('show_title', '').strip()
        season = request.GET.get('season', '')
        indexer = request.GET.get('indexer', 'all')
        filter_results = request.GET.get('filter', 'true').lower() == 'true'

        # Validate parameters
        if not show_title:
            return JsonResponse({'error': 'show_title is required'}, status=400)

        if not season:
            return JsonResponse({'error': 'season is required'}, status=400)

        try:
            season_num = int(season)
        except ValueError:
            return JsonResponse({'error': 'season must be an integer'}, status=400)

        # Initialize Jackett service
        jackett_service = JackettService()

        # Perform season search
        results = jackett_service.search_season(show_title, season_num, indexer, filter_results)

        # Convert results to JSON-serializable format
        results_data = [result.to_dict() for result in results]

        return JsonResponse({
            'success': True,
            'results': results_data,
            'total_results': len(results_data),
            'search_query': f"{show_title} Season {season_num}",
            'search_type': 'season',
            'filtered': filter_results,
        })

    except JackettAPIError as e:
        logger.error(f"Jackett season search failed: {e}")
        return JsonResponse({
            'error': f'Jackett season search failed: {str(e)}'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in Jackett season search: {e}")
        return JsonResponse({
            'error': f'Unexpected error: {str(e)}'
        }, status=500)


def api_profiles_list(request):
    """API endpoint to get list of active profiles"""
    try:
        from .models import QualityProfile

        profiles = QualityProfile.objects.filter(is_active=True).order_by('name')

        profiles_data = []
        for profile in profiles:
            profiles_data.append({
                'id': profile.id,
                'name': profile.name,
                'description': profile.description,
                'is_default': profile.is_default,
                'allow_upgrades': profile.allow_upgrades,
                'upgrade_until_quality': profile.upgrade_until_quality.name if profile.upgrade_until_quality else None
            })

        return JsonResponse({
            'success': True,
            'profiles': profiles_data
        })

    except Exception as e:
        logger.error(f"Failed to get profiles list: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
